"""
成都项目数据分析工具模块
包含日志配置、数据验证、文件操作等通用工具函数
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import sys

from config import LOGGING_CONFIG, DATA_CONFIG, VALIDATION_CONFIG


class ChengduAnalysisLogger:
    """成都项目分析专用日志记录器"""
    
    def __init__(self, name: str = "ChengduAnalysis"):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
        """
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, LOGGING_CONFIG['level']))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器（文件和控制台）"""
        formatter = logging.Formatter(LOGGING_CONFIG['format'])
        
        # 控制台处理器
        if LOGGING_CONFIG['console_handler']:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # 文件处理器
        if LOGGING_CONFIG['file_handler']:
            log_file = DATA_CONFIG['log_dir'] / f"chengdu_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def info(self, message: str):
        """记录信息级别日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告级别日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误级别日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试级别日志"""
        self.logger.debug(message)


class DataValidator:
    """数据验证工具类"""
    
    def __init__(self, logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化数据验证器
        
        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or ChengduAnalysisLogger("DataValidator")
    
    def validate_dataframe_structure(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证DataFrame的结构是否符合要求
        
        Args:
            df: 待验证的DataFrame
            
        Returns:
            Tuple[bool, List[str]]: (验证是否通过, 错误信息列表)
        """
        errors = []
        
        # 检查必需列是否存在
        required_columns = set(VALIDATION_CONFIG['required_columns'])
        actual_columns = set(df.columns)
        missing_columns = required_columns - actual_columns
        
        if missing_columns:
            error_msg = f"缺少必需列: {list(missing_columns)}"
            errors.append(error_msg)
            self.logger.error(error_msg)
        
        # 检查数据行数
        if len(df) == 0:
            error_msg = "数据框为空，没有数据行"
            errors.append(error_msg)
            self.logger.error(error_msg)
        
        # 检查缺失值比例
        if not VALIDATION_CONFIG['allow_missing_values']:
            missing_counts = df.isnull().sum()
            columns_with_missing = missing_counts[missing_counts > 0]
            
            if len(columns_with_missing) > 0:
                error_msg = f"发现缺失值的列: {dict(columns_with_missing)}"
                errors.append(error_msg)
                self.logger.warning(error_msg)
        
        validation_passed = len(errors) == 0
        
        if validation_passed:
            self.logger.info(f"✓ 数据结构验证通过，共 {len(df)} 行数据，{len(df.columns)} 列")
        else:
            self.logger.error(f"✗ 数据结构验证失败，发现 {len(errors)} 个问题")
        
        return validation_passed, errors
    
    def validate_data_types(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        验证数据类型是否正确
        
        Args:
            df: 待验证的DataFrame
            
        Returns:
            Tuple[bool, List[str]]: (验证是否通过, 错误信息列表)
        """
        errors = []
        
        # 检查数值列的数据类型
        for col in VALIDATION_CONFIG['numeric_columns']:
            if col in df.columns:
                if not pd.api.types.is_numeric_dtype(df[col]):
                    try:
                        # 尝试转换为数值类型
                        pd.to_numeric(df[col], errors='raise')
                    except (ValueError, TypeError):
                        error_msg = f"列 '{col}' 包含非数值数据，无法转换为数值类型"
                        errors.append(error_msg)
                        self.logger.error(error_msg)
        
        # 检查日期列的数据类型
        for col in VALIDATION_CONFIG['date_columns']:
            if col in df.columns:
                if not pd.api.types.is_datetime64_any_dtype(df[col]):
                    try:
                        # 尝试转换为日期类型
                        pd.to_datetime(df[col], errors='raise')
                    except (ValueError, TypeError):
                        error_msg = f"列 '{col}' 包含无效日期数据，无法转换为日期类型"
                        errors.append(error_msg)
                        self.logger.error(error_msg)
        
        validation_passed = len(errors) == 0
        
        if validation_passed:
            self.logger.info("✓ 数据类型验证通过")
        else:
            self.logger.error(f"✗ 数据类型验证失败，发现 {len(errors)} 个问题")
        
        return validation_passed, errors


def ensure_directory_exists(directory_path: Path) -> bool:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        directory_path: 目录路径
        
    Returns:
        bool: 操作是否成功
    """
    try:
        directory_path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        print(f"创建目录失败 {directory_path}: {e}")
        return False


def format_regression_results(results_dict: Dict[str, Any], decimal_places: int = 4) -> Dict[str, Any]:
    """
    格式化回归分析结果，统一小数位数
    
    Args:
        results_dict: 回归结果字典
        decimal_places: 保留小数位数
        
    Returns:
        Dict[str, Any]: 格式化后的结果字典
    """
    formatted_results = {}
    
    for key, value in results_dict.items():
        if isinstance(value, (int, float, np.number)):
            formatted_results[key] = round(float(value), decimal_places)
        elif isinstance(value, (list, tuple, np.ndarray)):
            formatted_results[key] = [round(float(v), decimal_places) if isinstance(v, (int, float, np.number)) else v for v in value]
        elif isinstance(value, dict):
            formatted_results[key] = format_regression_results(value, decimal_places)
        else:
            formatted_results[key] = value
    
    return formatted_results


def get_current_timestamp() -> str:
    """
    获取当前时间戳字符串
    
    Returns:
        str: 格式化的时间戳字符串
    """
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


if __name__ == "__main__":
    # 测试日志功能
    logger = ChengduAnalysisLogger("TestLogger")
    logger.info("工具模块测试完成")
    
    # 测试目录创建
    from config import create_directories
    create_directories()
    print("✓ 工具模块初始化完成")
