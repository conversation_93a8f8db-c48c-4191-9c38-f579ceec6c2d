{"analysis_timestamp": "2025-08-01 11:59:11", "data_summary": {"total_observations": 164, "data_columns": ["id", "date", "ln_total_visitors", "logit_night_ratio", "avg_customer_spending", "price_ratio", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "holiday_day_idx_c", "holiday_day_idx_c_sq", "is_2023", "is_2024", "temperature_c", "temperature_c_sq", "precipitation", "created_at"], "date_range": {"start_date": "2023-07-01 00:00:00", "end_date": "2025-07-31 00:00:00"}}, "models": {"ln_total_visitors_model": {"model_name": "ln_total_visitors_model", "dependent_variable": "ln_total_visitors", "independent_variables": ["avg_customer_spending", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "is_2023", "is_2024", "temperature_c", "temperature_c_sq", "precipitation", "holiday_day_idx_c", "holiday_day_idx_c_sq"], "sample_size": 164.0, "timestamp": "2025-08-01 11:59:11", "coefficients": {"values": {"const": 8.8494, "avg_customer_spending": 0.0004, "monday": -0.414, "tuesday": -0.4507, "wednesday": -0.4719, "thursday": -0.4957, "friday": -0.5082, "saturday": 0.0947, "is_2023": 0.1935, "is_2024": 0.2851, "temperature_c": 0.0056, "temperature_c_sq": -0.002, "precipitation": -0.0019, "holiday_day_idx_c": 0.0035, "holiday_day_idx_c_sq": -0.0009}, "std_errors": {"const": 0.4542, "avg_customer_spending": 0.0029, "monday": 0.0723, "tuesday": 0.0721, "wednesday": 0.0717, "thursday": 0.072, "friday": 0.0735, "saturday": 0.0711, "is_2023": 0.1073, "is_2024": 0.0907, "temperature_c": 0.0081, "temperature_c_sq": 0.0023, "precipitation": 0.0019, "holiday_day_idx_c": 0.0014, "holiday_day_idx_c_sq": 0.0001}, "t_values": {"const": 19.4828, "avg_customer_spending": 0.1319, "monday": -5.7304, "tuesday": -6.247, "wednesday": -6.5786, "thursday": -6.8852, "friday": -6.9185, "saturday": 1.3311, "is_2023": 1.803, "is_2024": 3.1449, "temperature_c": 0.6846, "temperature_c_sq": -0.8774, "precipitation": -1.0282, "holiday_day_idx_c": 2.5144, "holiday_day_idx_c_sq": -13.4999}, "p_values": {"const": 0.0, "avg_customer_spending": 0.8953, "monday": 0.0, "tuesday": 0.0, "wednesday": 0.0, "thursday": 0.0, "friday": 0.0, "saturday": 0.1852, "is_2023": 0.0734, "is_2024": 0.002, "temperature_c": 0.4947, "temperature_c_sq": 0.3817, "precipitation": 0.3055, "holiday_day_idx_c": 0.013, "holiday_day_idx_c_sq": 0.0}, "confidence_intervals": {"const": [7.9519, 9.747], "avg_customer_spending": [-0.0054, 0.0062], "monday": [-0.5568, -0.2713], "tuesday": [-0.5933, -0.3082], "wednesday": [-0.6137, -0.3302], "thursday": [-0.638, -0.3535], "friday": [-0.6533, -0.363], "saturday": [-0.0459, 0.2352], "is_2023": [-0.0186, 0.4056], "is_2024": [0.106, 0.4642], "temperature_c": [-0.0105, 0.0216], "temperature_c_sq": [-0.0064, 0.0025], "precipitation": [-0.0056, 0.0018], "holiday_day_idx_c": [0.0007, 0.0062], "holiday_day_idx_c_sq": [-0.001, -0.0007]}}, "model_fit": {"r_squared": 0.723, "adjusted_r_squared": 0.6969, "f_statistic": 27.7759, "f_pvalue": 0.0, "aic": 21.0028, "bic": 67.5008, "log_likelihood": 4.4986}, "residuals": {"residuals": [0.3505, 0.3039, 0.138, 0.2056, 0.211, 0.2637, 0.2803, 0.2622, 0.1743, 0.1172, -0.0166, 0.1235, 0.016, 0.1315, -0.021, -0.0286, -0.1356, -0.1875, -0.1726, -0.1501, -0.2122, -0.2688, -0.3432, -0.4047, -0.2903, -0.282, -0.2146, -0.2576, -0.4314, -0.4013, -0.3386, -0.2744, -0.2013, -0.1492, -0.0819, -0.2921, -0.2295, -0.1215, -0.0422, 0.0368, 0.1755, 0.4397, 0.2499, 0.2662, 0.3703, 0.3369, 0.423, 0.4235, 0.4224, 0.2673, 0.2715, 0.2117, 0.2499, 0.2808, 0.3813, 0.2752, 0.2488, 0.2801, 0.2308, 0.1388, -0.3073, -0.7202, -1.1106, -0.3461, -0.5251, 0.2065, 0.1168, 0.0058, -0.0407, -0.029, -0.0156, -0.0296, -0.0523, 0.0286, -0.0924, -0.0742, -0.2298, -0.0499, -0.0894, -0.0764, -0.1767, -0.1873, -0.19, -0.152, -0.2675, -0.1137, -0.1592, -0.0527, -0.1678, -0.2465, -0.2089, -0.0824, -0.0695, -0.1681, -0.1231, 0.0097, 0.0403, 0.1107, 0.1063, 0.1286, -0.0034, 0.0567, 0.2436, 0.221, 0.2739, 0.2583, 0.3425, 0.1599, 0.0786, 0.1675, 0.2093, 0.2508, 0.3637, 0.3253, 0.0898, 0.0969, 0.1753, 0.2816, 0.2517, 0.2331, 0.1498, -0.0557, -0.0767, -0.1479, -0.1269, -0.1425, -0.2468, -0.2615, -0.2901, -0.4866, 0.1876, 0.0844, -0.0444, 0.0237, 0.0828, 0.0474, 0.0094, 0.0204, 0.2112, 0.013, -0.0645, -0.0177, 0.0168, 0.0151, 0.0447, 0.1534, -0.1755, -0.2095, -0.2673, -0.3932, -0.2807, -0.0215, 0.1379, 0.1074, 0.111, 0.02, 0.0163, -0.0129, 0.0983, 0.183, 0.0256, -0.0548, -0.0547, -0.0125], "standardized_residuals": [1.4192, 1.2304, 0.5585, 0.8325, 0.8542, 1.0675, 1.135, 1.0616, 0.7057, 0.4746, -0.0674, 0.5002, 0.0648, 0.5323, -0.0848, -0.116, -0.5489, -0.759, -0.6988, -0.6076, -0.8593, -1.0883, -1.3894, -1.6386, -1.1754, -1.1417, -0.869, -1.0429, -1.7468, -1.6246, -1.3708, -1.111, -0.815, -0.6039, -0.3316, -1.1826, -0.9292, -0.4918, -0.1709, 0.1491, 0.7106, 1.7802, 1.0117, 1.0779, 1.4994, 1.3638, 1.7125, 1.7147, 1.7103, 1.0823, 1.0993, 0.857, 1.0119, 1.1369, 1.544, 1.1141, 1.0075, 1.1339, 0.9343, 0.5618, -1.2443, -2.9159, -4.4966, -1.4011, -2.1261, 0.8361, 0.473, 0.0235, -0.1649, -0.1174, -0.063, -0.1198, -0.2116, 0.1156, -0.3742, -0.3004, -0.9306, -0.202, -0.3621, -0.3092, -0.7153, -0.7584, -0.7692, -0.6153, -1.0832, -0.4605, -0.6447, -0.2132, -0.6795, -0.998, -0.8459, -0.3334, -0.2814, -0.6805, -0.4983, 0.0393, 0.163, 0.4484, 0.4305, 0.5207, -0.0137, 0.2296, 0.9863, 0.8949, 1.1089, 1.0457, 1.3866, 0.6473, 0.3183, 0.6783, 0.8475, 1.0152, 1.4725, 1.317, 0.3637, 0.3921, 0.7097, 1.1401, 1.0192, 0.9436, 0.6066, -0.2257, -0.3105, -0.5988, -0.5137, -0.5768, -0.999, -1.0586, -1.1746, -1.97, 0.7597, 0.3416, -0.18, 0.0959, 0.3352, 0.192, 0.0379, 0.0828, 0.8549, 0.0524, -0.2612, -0.0715, 0.0678, 0.0611, 0.181, 0.6211, -0.7107, -0.8481, -1.0821, -1.5921, -1.1365, -0.0872, 0.5584, 0.4346, 0.4496, 0.0812, 0.066, -0.0523, 0.3979, 0.7408, 0.1035, -0.2219, -0.2214, -0.0505], "mean_squared_error": 0.0554, "root_mean_squared_error": 0.2354}, "predictions": {"fitted_values": [8.2114, 8.1762, 7.7556, 7.8215, 7.8683, 7.8971, 7.9263, 8.5834, 8.522, 8.1554, 8.1662, 8.0361, 7.9554, 8.1963, 8.8696, 8.8097, 8.4221, 8.4188, 8.4276, 8.4265, 8.4383, 9.0688, 8.9912, 8.594, 8.5804, 8.5456, 8.4193, 8.569, 9.1824, 9.0948, 8.6911, 8.6115, 8.5943, 8.6009, 8.6088, 9.2146, 9.084, 8.6967, 8.6547, 8.6184, 8.5881, 8.5633, 9.115, 9.046, 8.6165, 8.5633, 8.5157, 8.4679, 8.4334, 8.9953, 8.7975, 8.4531, 8.39, 8.3181, 8.1745, 8.1264, 8.6782, 8.6354, 8.173, 7.9668, 8.0273, 7.9737, 7.8464, 8.4699, 8.3363, 8.2897, 8.2668, 7.89, 7.9264, 7.893, 7.9622, 8.0181, 8.6669, 8.605, 8.2022, 8.1819, 8.2061, 8.2916, 8.3197, 8.8356, 8.8203, 8.4923, 8.4572, 8.4135, 8.5084, 8.5144, 9.117, 9.0057, 8.6459, 8.6711, 8.6371, 8.652, 8.6557, 9.2552, 9.0902, 8.7462, 8.7473, 8.7349, 8.7126, 8.6945, 9.3017, 9.2015, 8.789, 8.7416, 8.6985, 8.6778, 8.536, 9.2331, 9.1406, 8.7096, 8.6532, 8.6075, 8.4995, 8.5274, 9.1093, 8.9889, 8.5447, 8.4743, 8.4087, 8.3339, 8.2822, 8.8577, 8.7331, 8.2697, 8.1846, 8.1245, 8.0756, 8.0155, 8.5457, 8.4247, 8.0113, 7.7191, 7.6139, 7.6341, 7.6061, 7.6397, 7.6777, 8.2929, 8.3058, 7.8843, 7.954, 7.9908, 7.9986, 7.9889, 8.6667, 8.6064, 8.2176, 8.2058, 8.1924, 8.2044, 8.2184, 8.8405, 8.7843, 8.3853, 8.3678, 8.3691, 8.3, 8.3191, 8.9115, 8.886, 8.4704, 8.4467, 8.4206, 8.4025], "actual_values": [8.562, 8.4801, 7.8936, 8.0272, 8.0793, 8.1608, 8.2066, 8.8456, 8.6963, 8.2726, 8.1496, 8.1597, 7.9714, 8.3277, 8.8487, 8.7811, 8.2865, 8.2314, 8.255, 8.2764, 8.226, 8.8, 8.648, 8.1892, 8.29, 8.2636, 8.2047, 8.3114, 8.751, 8.6935, 8.3526, 8.3371, 8.393, 8.4517, 8.5269, 8.9225, 8.8545, 8.5753, 8.6125, 8.6552, 8.7636, 9.0029, 9.3649, 9.3123, 8.9868, 8.9001, 8.9387, 8.8914, 8.8558, 9.2626, 9.069, 8.6648, 8.6399, 8.599, 8.5558, 8.4016, 8.927, 8.9154, 8.4038, 8.1056, 7.72, 7.2535, 6.7358, 8.1239, 7.8112, 8.4962, 8.3837, 7.8958, 7.8857, 7.864, 7.9466, 7.9885, 8.6147, 8.6336, 8.1098, 8.1077, 7.9763, 8.2417, 8.2303, 8.7592, 8.6436, 8.305, 8.2672, 8.2615, 8.2409, 8.4007, 8.9578, 8.953, 8.478, 8.4246, 8.4281, 8.5696, 8.5862, 9.0872, 8.9671, 8.7559, 8.7875, 8.8456, 8.8189, 8.8231, 9.2984, 9.2582, 9.0326, 8.9626, 8.9723, 8.936, 8.8785, 9.393, 9.2192, 8.8771, 8.8625, 8.8582, 8.8632, 8.8527, 9.1992, 9.0858, 8.72, 8.7559, 8.6604, 8.5669, 8.4321, 8.8019, 8.6564, 8.1218, 8.0577, 7.9821, 7.8288, 7.7541, 8.2556, 7.9381, 8.1989, 7.8034, 7.5694, 7.6578, 7.6889, 7.6871, 7.6871, 8.3134, 8.517, 7.8973, 7.8895, 7.9732, 8.0153, 8.004, 8.7114, 8.7598, 8.0421, 7.9963, 7.9252, 7.8112, 7.9377, 8.8189, 8.9223, 8.4927, 8.4789, 8.3891, 8.3163, 8.3062, 9.0098, 9.069, 8.496, 8.3919, 8.3659, 8.39]}}, "logit_night_ratio_model": {"model_name": "logit_night_ratio_model", "dependent_variable": "logit_night_ratio", "independent_variables": ["price_ratio", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "is_2023", "is_2024", "temperature_c", "temperature_c_sq", "precipitation", "holiday_day_idx_c", "holiday_day_idx_c_sq"], "sample_size": 164.0, "timestamp": "2025-08-01 11:59:11", "coefficients": {"values": {"const": -4.3114, "price_ratio": 1.8852, "monday": 0.0607, "tuesday": 0.0857, "wednesday": 0.1219, "thursday": 0.0681, "friday": 0.1052, "saturday": 0.2575, "is_2023": -0.8619, "is_2024": -0.2319, "temperature_c": -0.0134, "temperature_c_sq": -0.0016, "precipitation": -0.0007, "holiday_day_idx_c": 0.004, "holiday_day_idx_c_sq": 0.0001}, "std_errors": {"const": 0.2371, "price_ratio": 0.19, "monday": 0.069, "tuesday": 0.0689, "wednesday": 0.0687, "thursday": 0.0689, "friday": 0.0698, "saturday": 0.0681, "is_2023": 0.0574, "is_2024": 0.0575, "temperature_c": 0.0078, "temperature_c_sq": 0.0022, "precipitation": 0.0018, "holiday_day_idx_c": 0.0013, "holiday_day_idx_c_sq": 0.0001}, "t_values": {"const": -18.1823, "price_ratio": 9.9202, "monday": 0.8793, "tuesday": 1.2439, "wednesday": 1.7749, "thursday": 0.9875, "friday": 1.5065, "saturday": 3.7801, "is_2023": -15.0057, "is_2024": -4.0357, "temperature_c": -1.7071, "temperature_c_sq": -0.7334, "precipitation": -0.4129, "holiday_day_idx_c": 3.0855, "holiday_day_idx_c_sq": 2.1203}, "p_values": {"const": 0.0, "price_ratio": 0.0, "monday": 0.3806, "tuesday": 0.2155, "wednesday": 0.0779, "thursday": 0.325, "friday": 0.1341, "saturday": 0.0002, "is_2023": 0.0, "is_2024": 0.0001, "temperature_c": 0.0899, "temperature_c_sq": 0.4645, "precipitation": 0.6803, "holiday_day_idx_c": 0.0024, "holiday_day_idx_c_sq": 0.0356}, "confidence_intervals": {"const": [-4.78, -3.8429], "price_ratio": [1.5097, 2.2607], "monday": [-0.0757, 0.1971], "tuesday": [-0.0505, 0.2219], "wednesday": [-0.0138, 0.2577], "thursday": [-0.0681, 0.2043], "friday": [-0.0328, 0.2432], "saturday": [0.1229, 0.3921], "is_2023": [-0.9754, -0.7484], "is_2024": [-0.3454, -0.1183], "temperature_c": [-0.0288, 0.0021], "temperature_c_sq": [-0.006, 0.0027], "precipitation": [-0.0043, 0.0028], "holiday_day_idx_c": [0.0015, 0.0066], "holiday_day_idx_c_sq": [0.0, 0.0003]}}, "model_fit": {"r_squared": 0.6934, "adjusted_r_squared": 0.6646, "f_statistic": 24.0734, "f_pvalue": 0.0, "aic": 7.6245, "bic": 54.1225, "log_likelihood": 11.1878}, "residuals": {"residuals": [-0.285, -0.0985, -0.2401, -0.3072, -0.2732, -0.0942, -0.6906, -0.2094, -0.0544, -0.1225, -0.2077, -0.3092, 0.0662, 0.1646, 0.1233, 0.1279, -0.0327, 0.3646, -0.042, 0.0556, 0.2725, -0.0527, 0.0909, 0.0521, -0.1676, -0.1561, 0.0756, 0.1095, -0.0623, 0.2648, 0.0292, 0.0865, -0.3806, 0.1148, 0.1942, 0.0522, 0.0126, 0.3216, 0.0918, 0.0665, 0.3262, -0.0052, 0.0631, 0.1918, 0.0713, -0.042, -0.1423, -0.133, -0.1536, -0.1681, -0.3903, -0.156, -0.084, -0.1513, -0.2744, 0.1544, 0.1351, 0.0368, 0.0792, 0.0105, 0.2903, 0.4686, 0.3927, 0.4079, 0.1215, 0.0055, 0.1357, 0.4789, 0.2138, 0.1687, 0.1456, 0.1009, 0.255, 0.2202, 0.1699, 0.553, 0.4697, 0.2888, 0.3473, 0.0125, -0.1544, -0.1022, 0.0764, 0.0993, 0.2522, -0.0043, 0.3843, -0.0235, 0.1693, 0.1687, 0.3283, 0.1936, -0.1776, -0.2099, -0.2505, -0.1178, -0.1708, 0.3573, -0.1484, -0.2102, -0.502, -0.1153, -0.1959, -0.1497, -0.4358, -0.108, -0.2058, -0.1625, -0.1627, -0.1764, -0.3311, -0.1395, -0.3405, -0.258, -0.2234, -0.264, -0.1546, -0.2822, -0.0264, 0.119, 0.2132, -0.1518, -0.0088, -0.2709, 0.1022, -0.0275, 0.0272, -0.0265, 0.1441, 0.0885, -0.0211, 0.0287, -0.0631, 0.0776, -0.0984, -0.8157, -0.0257, 0.0448, -0.2337, 0.134, -0.3521, -0.2162, -0.3236, -0.3444, -0.1262, -0.0181, -0.1837, 0.0011, 0.1869, -0.2196, 0.0391, 0.4225, 0.2478, 0.1971, 0.3599, 0.2588, 0.2539, 0.1135, 0.1241, 0.2071, 0.1133, -0.0117, 0.1726, 0.0703], "standardized_residuals": [-1.2019, -0.4156, -1.0125, -1.2953, -1.1521, -0.3973, -2.9126, -0.8832, -0.2296, -0.5167, -0.876, -1.3042, 0.2791, 0.6942, 0.5199, 0.5395, -0.1381, 1.5375, -0.1772, 0.2345, 1.1493, -0.2223, 0.3834, 0.2197, -0.707, -0.6583, 0.3188, 0.4618, -0.2629, 1.1169, 0.1233, 0.365, -1.605, 0.4841, 0.8191, 0.2199, 0.0533, 1.3565, 0.3872, 0.2807, 1.3757, -0.0219, 0.2661, 0.8088, 0.3009, -0.1773, -0.5999, -0.5609, -0.6477, -0.7091, -1.6459, -0.658, -0.3541, -0.6383, -1.1573, 0.651, 0.5697, 0.155, 0.3341, 0.0444, 1.2241, 1.9761, 1.656, 1.7203, 0.5123, 0.0232, 0.5723, 2.0197, 0.9015, 0.7114, 0.614, 0.4255, 1.0754, 0.9286, 0.7165, 2.3322, 1.9807, 1.2181, 1.4645, 0.0529, -0.6513, -0.4308, 0.3221, 0.4189, 1.0636, -0.0179, 1.6205, -0.0992, 0.7138, 0.7116, 1.3844, 0.8164, -0.7488, -0.8852, -1.0563, -0.4968, -0.7202, 1.507, -0.6261, -0.8864, -2.1173, -0.4862, -0.826, -0.6315, -1.8377, -0.4556, -0.868, -0.6852, -0.686, -0.7441, -1.3963, -0.5881, -1.4361, -1.0881, -0.9423, -1.1132, -0.6521, -1.1902, -0.1113, 0.5017, 0.899, -0.6401, -0.0371, -1.1427, 0.4309, -0.1158, 0.1146, -0.1119, 0.6079, 0.3731, -0.0889, 0.1208, -0.2661, 0.3274, -0.4151, -3.4402, -0.1082, 0.189, -0.9855, 0.565, -1.4848, -0.9119, -1.3645, -1.4523, -0.532, -0.0764, -0.7748, 0.0048, 0.7884, -0.926, 0.1649, 1.7819, 1.0449, 0.8313, 1.5176, 1.0913, 1.0709, 0.4786, 0.5235, 0.8734, 0.478, -0.0494, 0.728, 0.2963], "mean_squared_error": 0.0511, "root_mean_squared_error": 0.226}, "predictions": {"fitted_values": [-2.6139, -2.8991, -2.7858, -2.7774, -2.7691, -2.8935, -2.8329, -2.7387, -3.0612, -2.9395, -2.8527, -2.8605, -2.9256, -2.699, -2.4795, -2.7621, -2.7606, -2.742, -2.6814, -2.7111, -2.7294, -2.614, -2.9162, -2.7751, -2.8101, -2.6176, -2.7318, -2.7578, -2.396, -2.6431, -2.6825, -2.6204, -2.544, -2.4721, -2.459, -2.1633, -2.4637, -2.5124, -2.3878, -2.2754, -2.3063, -2.2235, -2.0938, -2.5396, -2.5048, -2.3295, -2.2995, -2.301, -2.3306, -2.0793, -2.3429, -2.6069, -2.4308, -2.3796, -2.5396, -2.6007, -2.3387, -2.6198, -2.6203, -2.6108, -2.5549, -2.577, -2.1872, -2.0947, -2.2309, -2.234, -2.5444, -2.5487, -2.4487, -2.3927, -2.4435, -2.4577, -2.2672, -2.4776, -2.5224, -2.5072, -2.4121, -2.5026, -2.5132, -1.2665, -2.7346, -2.4925, -2.4793, -2.4304, -2.4981, -2.674, -2.6413, -2.8109, -2.5717, -2.5604, -2.4238, -2.4097, -2.4619, -2.3272, -2.6442, -2.4918, -2.4556, -2.4597, -2.5043, -2.4861, -2.4796, -2.7067, -2.4865, -2.4922, -2.4216, -2.4449, -2.3773, -2.3288, -2.6696, -2.4073, -2.3305, -2.2963, -2.2477, -2.005, -1.8264, -2.1959, -2.3054, -2.32, -2.3332, -2.4027, -2.3449, -2.1477, -2.2635, -1.9452, -1.6852, -1.5576, -1.4219, -1.2613, -1.0373, -1.8694, -1.6912, -2.0719, -2.0286, -1.9878, -1.9703, -1.9261, -2.0973, -1.7484, -1.9501, -2.0571, -1.8828, -1.8823, -2.0841, -1.9626, -1.7974, -2.1727, -2.102, -2.2059, -2.2861, -2.2033, -2.1014, -1.9743, -2.0715, -2.177, -2.1885, -2.0237, -2.0264, -2.0462, -1.7817, -2.166, -2.2518, -2.151, -2.251, -2.226], "actual_values": [-2.8989, -2.9977, -3.0259, -3.0846, -3.0423, -2.9877, -3.5236, -2.9481, -3.1156, -3.0621, -3.0604, -3.1697, -2.8595, -2.5344, -2.3562, -2.6341, -2.7934, -2.3775, -2.7234, -2.6555, -2.4568, -2.6667, -2.8253, -2.723, -2.9777, -2.7737, -2.6562, -2.6483, -2.4583, -2.3783, -2.6533, -2.5339, -2.9246, -2.3574, -2.2648, -2.1112, -2.4511, -2.1907, -2.296, -2.2089, -1.9801, -2.2287, -2.0307, -2.3478, -2.4335, -2.3716, -2.4418, -2.434, -2.4841, -2.2475, -2.7331, -2.7629, -2.5148, -2.5309, -2.814, -2.4464, -2.2036, -2.583, -2.5411, -2.6003, -2.2646, -2.1084, -1.7945, -1.6868, -2.1094, -2.2285, -2.4087, -2.0698, -2.235, -2.224, -2.2979, -2.3568, -2.0122, -2.2574, -2.3525, -1.9542, -1.9424, -2.2137, -2.1659, -1.254, -2.8891, -2.5946, -2.4029, -2.331, -2.2459, -2.6782, -2.257, -2.8344, -2.4024, -2.3917, -2.0955, -2.2161, -2.6395, -2.5371, -2.8947, -2.6096, -2.6264, -2.1023, -2.6528, -2.6963, -2.9817, -2.822, -2.6823, -2.6419, -2.8574, -2.553, -2.5831, -2.4912, -2.8323, -2.5837, -2.6616, -2.4357, -2.5882, -2.263, -2.0499, -2.4599, -2.46, -2.6022, -2.3596, -2.2837, -2.1317, -2.2994, -2.2723, -2.2162, -1.5831, -1.585, -1.3948, -1.2879, -0.8931, -1.781, -1.7122, -2.0432, -2.0917, -1.9102, -2.0687, -2.7418, -2.123, -1.7036, -2.1837, -1.9232, -2.2348, -2.0986, -2.4077, -2.307, -1.9235, -2.1908, -2.2857, -2.2047, -2.0991, -2.4229, -2.0623, -1.5518, -1.8237, -1.9798, -1.8286, -1.765, -1.7725, -1.9327, -1.6576, -1.9589, -2.1385, -2.1627, -2.0784, -2.1558]}}}, "analysis_summary": {"total_models": 2, "successful_models": ["ln_total_visitors_model", "logit_night_ratio_model"], "model_comparison": {"ln_total_visitors_model": {"r_squared": 0.723, "adjusted_r_squared": 0.6969, "f_statistic": 27.7759, "f_pvalue": 0.0, "aic": 21.0028, "sample_size": 164.0}, "logit_night_ratio_model": {"r_squared": 0.6934, "adjusted_r_squared": 0.6646, "f_statistic": 24.0734, "f_pvalue": 0.0, "aic": 7.6245, "sample_size": 164.0}}}}