"""
成都项目数据分析配置文件
包含数据库配置、文件路径、模型参数等配置信息
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据文件配置
DATA_CONFIG = {
    'excel_file_path': PROJECT_ROOT / 'data_chengdu_v2.xlsx',
    'database_path': PROJECT_ROOT / 'chengdu_analysis.db',
    'output_dir': PROJECT_ROOT / 'output',
    'log_dir': PROJECT_ROOT / 'logs'
}

# 数据库配置
DATABASE_CONFIG = {
    'table_name': 'chengdu_summer_data',
    'connection_timeout': 30,
    'enable_foreign_keys': True
}

# Excel文件列名映射（中文列名到英文字段名）
COLUMN_MAPPING = {
    '日期': 'date',
    'ln总人次': 'ln_total_visitors',
    'logit夜场人次占比': 'logit_night_ratio',
    '平均客单': 'avg_customer_spending',
    '价格比': 'price_ratio',
    '星期一': 'monday',
    '星期二': 'tuesday', 
    '星期三': 'wednesday',
    '星期四': 'thursday',
    '星期五': 'friday',
    '星期六': 'saturday',
    'Holiday_Day_Idx_C': 'holiday_day_idx_c',
    'Holiday_Day_Idx_C_Sq': 'holiday_day_idx_c_sq',
    'Is_2023': 'is_2023',
    'Is_2024': 'is_2024',
    '温度_C': 'temperature_c',
    '温度_C_Sq': 'temperature_c_sq',
    '降水量': 'precipitation'
}

# 回归模型配置
MODEL_CONFIG = {
    # 模型1：ln总人次回归模型
    'ln_total_visitors_model': {
        'dependent_variable': 'ln_total_visitors',
        'independent_variables': [
            'avg_customer_spending',  # 专属自变量：平均客单
            # 星期几虚拟变量（以星期日为基准）
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
            # 年份虚拟变量（以2025年为基准）
            'is_2023', 'is_2024',
            # 温度及其二次项
            'temperature_c', 'temperature_c_sq',
            # 降水量
            'precipitation',
            # 假期天数及其二次项
            'holiday_day_idx_c', 'holiday_day_idx_c_sq'
        ]
    },
    
    # 模型2：logit夜场人次占比回归模型
    'logit_night_ratio_model': {
        'dependent_variable': 'logit_night_ratio',
        'independent_variables': [
            'price_ratio',  # 专属自变量：价格比
            # 星期几虚拟变量（以星期日为基准）
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
            # 年份虚拟变量（以2025年为基准）
            'is_2023', 'is_2024',
            # 温度及其二次项
            'temperature_c', 'temperature_c_sq',
            # 降水量
            'precipitation',
            # 假期天数及其二次项
            'holiday_day_idx_c', 'holiday_day_idx_c_sq'
        ]
    }
}

# 数据验证配置
VALIDATION_CONFIG = {
    'required_columns': list(COLUMN_MAPPING.keys()),
    'numeric_columns': [
        'ln总人次', 'logit夜场人次占比', '平均客单', '价格比',
        '星期一', '星期二', '星期三', '星期四', '星期五', '星期六',
        'Holiday_Day_Idx_C', 'Holiday_Day_Idx_C_Sq', 'Is_2023', 'Is_2024',
        '温度_C', '温度_C_Sq', '降水量'
    ],
    'date_columns': ['日期'],
    'allow_missing_values': False,
    'max_missing_ratio': 0.05  # 最大缺失值比例5%
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_handler': True,
    'console_handler': True
}

# 输出配置
OUTPUT_CONFIG = {
    'save_regression_results': True,
    'save_model_diagnostics': True,
    'save_data_summary': True,
    'export_format': ['csv', 'excel'],  # 支持的导出格式
    'decimal_places': 4  # 结果保留小数位数
}

# 创建必要的目录
def create_directories():
    """创建项目所需的目录结构"""
    directories = [
        DATA_CONFIG['output_dir'],
        DATA_CONFIG['log_dir']
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {directory}")

if __name__ == "__main__":
    create_directories()
    print("✓ 项目配置初始化完成")
