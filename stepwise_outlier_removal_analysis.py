"""
2025年异常样本逐步剔除回归分析
目标：通过逐步剔除异常样本，使价格比系数转为正值
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.api as sm
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

from data_processor import DataProcessor
from utils import ChengduAnalysisLogger

class StepwiseOutlierRemovalAnalysis:
    """逐步剔除异常样本分析类"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = ChengduAnalysisLogger("StepwiseOutlierRemoval")
        self.processor = DataProcessor()
        self.df = None
        self.year_2025_data = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 异常样本列表（基于方法5的结果）
        self.outlier_dates = [
            '2025-07-03',  # 最异常样本
            '2025-06-28',
            '2025-07-19', 
            '2025-07-05',
            '2025-07-26'
        ]
        
        # 回归变量
        self.base_vars = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                         'temperature_c', 'temperature_c_sq', 'precipitation', 
                         'holiday_day_idx_c', 'holiday_day_idx_c_sq']
        
        self.logger.info("逐步剔除异常样本分析器初始化完成")
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        self.df = self.processor.get_processed_data()
        if self.df is None:
            self.logger.error("无法加载数据")
            return False
        
        # 提取2025年数据
        self.year_2025_data = self.df[(self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)].copy()
        
        # 确保date列是datetime类型
        self.year_2025_data['date'] = pd.to_datetime(self.year_2025_data['date'])
        
        if len(self.year_2025_data) == 0:
            self.logger.error("没有找到2025年数据")
            return False
        
        self.logger.info(f"2025年数据样本数: {len(self.year_2025_data)}")
        
        # 验证异常样本是否存在
        outlier_count = 0
        for date_str in self.outlier_dates:
            date_obj = pd.to_datetime(date_str)
            if date_obj in self.year_2025_data['date'].values:
                outlier_count += 1
        
        self.logger.info(f"找到异常样本数: {outlier_count}/{len(self.outlier_dates)}")
        return True
    
    def run_regression(self, data_subset, description=""):
        """运行回归分析"""
        try:
            X = data_subset[['price_ratio'] + self.base_vars].dropna()
            y = data_subset.loc[X.index, 'logit_night_ratio']
            
            if len(X) < len(X.columns) + 1:  # 样本数不足
                return None
            
            X_with_const = sm.add_constant(X)
            model = sm.OLS(y, X_with_const).fit()
            
            price_coef = model.params.get('price_ratio', np.nan)
            price_pvalue = model.pvalues.get('price_ratio', np.nan)
            r_squared = model.rsquared
            
            return {
                'description': description,
                'sample_size': len(X),
                'price_coefficient': price_coef,
                'price_pvalue': price_pvalue,
                'r_squared': r_squared,
                'model': model,
                'X': X,
                'y': y
            }
        except Exception as e:
            self.logger.error(f"回归分析失败 ({description}): {str(e)}")
            return None
    
    def single_sample_removal_test(self):
        """单样本剔除测试"""
        self.logger.info("=" * 80)
        self.logger.info("步骤1：单样本剔除测试")
        self.logger.info("=" * 80)
        
        results = []
        
        # 基准模型（不剔除任何样本）
        baseline_result = self.run_regression(self.year_2025_data, "基准模型（无剔除）")
        if baseline_result:
            results.append(baseline_result)
            print(f"基准模型 - 样本数: {baseline_result['sample_size']}, "
                  f"价格比系数: {baseline_result['price_coefficient']:.6f}, "
                  f"p值: {baseline_result['price_pvalue']:.4f}, "
                  f"R²: {baseline_result['r_squared']:.4f}")
        
        # 逐一剔除每个异常样本
        for i, date_str in enumerate(self.outlier_dates):
            date_obj = pd.to_datetime(date_str)
            
            # 剔除当前样本
            subset_data = self.year_2025_data[self.year_2025_data['date'] != date_obj].copy()
            
            result = self.run_regression(subset_data, f"剔除样本{i+1}: {date_str}")
            if result:
                results.append(result)
                
                # 检查是否达到目标（系数为正）
                is_positive = result['price_coefficient'] > 0
                significance = "***" if result['price_pvalue'] < 0.001 else "**" if result['price_pvalue'] < 0.01 else "*" if result['price_pvalue'] < 0.05 else ""
                
                print(f"剔除 {date_str} - 样本数: {result['sample_size']}, "
                      f"价格比系数: {result['price_coefficient']:.6f} {'✓' if is_positive else '✗'}, "
                      f"p值: {result['price_pvalue']:.4f} {significance}, "
                      f"R²: {result['r_squared']:.4f}")
                
                if is_positive:
                    self.logger.info(f"🎯 单样本剔除成功！剔除 {date_str} 使系数转为正值")
        
        return results
    
    def double_sample_removal_test(self):
        """双样本剔除测试"""
        self.logger.info("=" * 80)
        self.logger.info("步骤2：双样本剔除测试")
        self.logger.info("=" * 80)
        
        results = []
        successful_combinations = []
        
        # 生成所有可能的两样本组合
        for combo in combinations(self.outlier_dates, 2):
            date_objs = [pd.to_datetime(date_str) for date_str in combo]
            
            # 剔除当前组合的样本
            subset_data = self.year_2025_data.copy()
            for date_obj in date_objs:
                subset_data = subset_data[subset_data['date'] != date_obj]
            
            combo_str = " + ".join(combo)
            result = self.run_regression(subset_data, f"剔除组合: {combo_str}")
            
            if result:
                results.append(result)
                
                # 检查是否达到目标
                is_positive = result['price_coefficient'] > 0
                significance = "***" if result['price_pvalue'] < 0.001 else "**" if result['price_pvalue'] < 0.01 else "*" if result['price_pvalue'] < 0.05 else ""
                
                print(f"剔除 {combo_str} - 样本数: {result['sample_size']}, "
                      f"价格比系数: {result['price_coefficient']:.6f} {'✓' if is_positive else '✗'}, "
                      f"p值: {result['price_pvalue']:.4f} {significance}, "
                      f"R²: {result['r_squared']:.4f}")
                
                if is_positive:
                    successful_combinations.append((combo, result))
                    self.logger.info(f"🎯 双样本剔除成功！剔除 {combo_str} 使系数转为正值")
        
        return results, successful_combinations
    
    def multi_sample_removal_test(self, n_samples):
        """多样本剔除测试"""
        self.logger.info("=" * 80)
        self.logger.info(f"步骤{n_samples+1}：{n_samples}样本剔除测试")
        self.logger.info("=" * 80)
        
        results = []
        successful_combinations = []
        
        # 生成所有可能的n样本组合
        for combo in combinations(self.outlier_dates, n_samples):
            date_objs = [pd.to_datetime(date_str) for date_str in combo]
            
            # 剔除当前组合的样本
            subset_data = self.year_2025_data.copy()
            for date_obj in date_objs:
                subset_data = subset_data[subset_data['date'] != date_obj]
            
            combo_str = " + ".join(combo)
            result = self.run_regression(subset_data, f"剔除组合: {combo_str}")
            
            if result:
                results.append(result)
                
                # 检查是否达到目标
                is_positive = result['price_coefficient'] > 0
                significance = "***" if result['price_pvalue'] < 0.001 else "**" if result['price_pvalue'] < 0.01 else "*" if result['price_pvalue'] < 0.05 else ""
                
                print(f"剔除 {combo_str} - 样本数: {result['sample_size']}, "
                      f"价格比系数: {result['price_coefficient']:.6f} {'✓' if is_positive else '✗'}, "
                      f"p值: {result['price_pvalue']:.4f} {significance}, "
                      f"R²: {result['r_squared']:.4f}")
                
                if is_positive:
                    successful_combinations.append((combo, result))
                    self.logger.info(f"🎯 {n_samples}样本剔除成功！剔除 {combo_str} 使系数转为正值")
        
        return results, successful_combinations
    
    def create_comparison_visualization(self, all_results, successful_combinations):
        """创建对比可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 系数变化图
        descriptions = [r['description'] for r in all_results if r is not None]
        coefficients = [r['price_coefficient'] for r in all_results if r is not None]
        
        # 只显示前15个结果以避免图表过于拥挤
        if len(descriptions) > 15:
            descriptions = descriptions[:15]
            coefficients = coefficients[:15]
        
        colors = ['red' if coef < 0 else 'green' for coef in coefficients]
        bars = axes[0, 0].bar(range(len(descriptions)), coefficients, color=colors, alpha=0.7)
        axes[0, 0].set_xlabel('剔除方案')
        axes[0, 0].set_ylabel('价格比系数')
        axes[0, 0].set_title('不同剔除方案的价格比系数对比')
        axes[0, 0].axhline(y=0, color='black', linestyle='--', alpha=0.7)
        axes[0, 0].tick_params(axis='x', rotation=45)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, coef) in enumerate(zip(bars, coefficients)):
            axes[0, 0].text(bar.get_x() + bar.get_width()/2, 
                           bar.get_height() + (0.01 if coef >= 0 else -0.03), 
                           f'{coef:.3f}', ha='center', 
                           va='bottom' if coef >= 0 else 'top', fontsize=8)
        
        # 2. R²变化图
        r_squared_values = [r['r_squared'] for r in all_results if r is not None][:15]
        
        bars2 = axes[0, 1].bar(range(len(descriptions)), r_squared_values, alpha=0.7, color='blue')
        axes[0, 1].set_xlabel('剔除方案')
        axes[0, 1].set_ylabel('R²')
        axes[0, 1].set_title('不同剔除方案的R²对比')
        axes[0, 1].tick_params(axis='x', rotation=45)
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 样本数变化图
        sample_sizes = [r['sample_size'] for r in all_results if r is not None][:15]
        
        bars3 = axes[1, 0].bar(range(len(descriptions)), sample_sizes, alpha=0.7, color='orange')
        axes[1, 0].set_xlabel('剔除方案')
        axes[1, 0].set_ylabel('剩余样本数')
        axes[1, 0].set_title('不同剔除方案的样本数对比')
        axes[1, 0].tick_params(axis='x', rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 散点图对比（基准 vs 最佳方案）
        if successful_combinations:
            # 选择第一个成功的组合
            best_combo, best_result = successful_combinations[0]
            
            # 基准数据
            baseline_result = all_results[0]  # 第一个是基准模型
            
            # 绘制基准散点图
            axes[1, 1].scatter(baseline_result['X']['price_ratio'], baseline_result['y'], 
                              alpha=0.7, s=50, color='red', label='基准模型（所有样本）')
            
            # 绘制最佳方案散点图
            axes[1, 1].scatter(best_result['X']['price_ratio'], best_result['y'], 
                              alpha=0.7, s=50, color='green', label=f'最佳方案（剔除{len(best_combo)}个样本）')
            
            # 添加回归线
            X_baseline = baseline_result['X']['price_ratio']
            y_baseline = baseline_result['y']
            z_baseline = np.polyfit(X_baseline, y_baseline, 1)
            p_baseline = np.poly1d(z_baseline)
            
            X_best = best_result['X']['price_ratio']
            y_best = best_result['y']
            z_best = np.polyfit(X_best, y_best, 1)
            p_best = np.poly1d(z_best)
            
            x_range = np.linspace(min(X_baseline.min(), X_best.min()), 
                                 max(X_baseline.max(), X_best.max()), 100)
            
            axes[1, 1].plot(x_range, p_baseline(x_range), "r--", alpha=0.8, 
                           label=f'基准回归线 (β={baseline_result["price_coefficient"]:.3f})')
            axes[1, 1].plot(x_range, p_best(x_range), "g--", alpha=0.8, 
                           label=f'最佳回归线 (β={best_result["price_coefficient"]:.3f})')
            
            axes[1, 1].set_xlabel('价格比')
            axes[1, 1].set_ylabel('logit夜场占比')
            axes[1, 1].set_title('基准模型 vs 最佳剔除方案对比')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('output/stepwise_outlier_removal_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_removed_samples_characteristics(self, successful_combinations):
        """分析被剔除样本的共同特征"""
        self.logger.info("=" * 80)
        self.logger.info("被剔除样本特征分析")
        self.logger.info("=" * 80)
        
        if not successful_combinations:
            print("没有找到成功的剔除组合")
            return
        
        # 选择最小的成功组合（剔除样本数最少）
        min_combo = min(successful_combinations, key=lambda x: len(x[0]))
        best_combo, best_result = min_combo
        
        print(f"最小成功剔除组合: {' + '.join(best_combo)}")
        print(f"剔除样本数: {len(best_combo)}")
        print(f"最终价格比系数: {best_result['price_coefficient']:.6f}")
        print(f"最终p值: {best_result['price_pvalue']:.4f}")
        print(f"最终R²: {best_result['r_squared']:.4f}")
        
        # 分析被剔除样本的特征
        removed_dates = [pd.to_datetime(date_str) for date_str in best_combo]
        removed_samples = self.year_2025_data[self.year_2025_data['date'].isin(removed_dates)]
        
        print(f"\n被剔除样本详情:")
        for _, sample in removed_samples.iterrows():
            weekday_map = {0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五', 5: '星期六', 6: '星期日'}
            weekday = weekday_map[sample['date'].dayofweek]
            print(f"  {sample['date'].strftime('%Y-%m-%d')} ({weekday}) - "
                  f"价格比: {sample['price_ratio']:.4f}, logit占比: {sample['logit_night_ratio']:.4f}")
        
        # 特征统计
        print(f"\n被剔除样本特征统计:")
        print(f"  平均价格比: {removed_samples['price_ratio'].mean():.4f}")
        print(f"  平均logit占比: {removed_samples['logit_night_ratio'].mean():.4f}")
        print(f"  价格比标准差: {removed_samples['price_ratio'].std():.4f}")
        print(f"  logit占比标准差: {removed_samples['logit_night_ratio'].std():.4f}")
        
        # 星期几分布
        weekday_counts = {}
        for _, sample in removed_samples.iterrows():
            weekday_map = {0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五', 5: '星期六', 6: '星期日'}
            weekday = weekday_map[sample['date'].dayofweek]
            weekday_counts[weekday] = weekday_counts.get(weekday, 0) + 1
        
        print(f"\n星期几分布:")
        for weekday, count in weekday_counts.items():
            print(f"  {weekday}: {count}个样本")
        
        return best_combo, best_result

    def run_comprehensive_stepwise_analysis(self):
        """运行综合逐步剔除分析"""
        self.logger.info("开始运行2025年异常样本逐步剔除回归分析")

        if not self.load_and_prepare_data():
            return None

        all_results = []
        all_successful_combinations = []

        # 步骤1：单样本剔除测试
        single_results = self.single_sample_removal_test()
        all_results.extend(single_results)

        # 检查单样本剔除是否已经成功
        single_success = [r for r in single_results[1:] if r and r['price_coefficient'] > 0]  # 跳过基准模型
        if single_success:
            self.logger.info("单样本剔除已达到目标，跳过后续测试")
            # 找到最佳单样本剔除方案
            best_single = min(single_success, key=lambda x: abs(x['price_coefficient']))
            # 从描述中提取日期
            date_str = best_single['description'].split(': ')[1]
            all_successful_combinations.append(([date_str], best_single))
        else:
            # 步骤2：双样本剔除测试
            double_results, double_success = self.double_sample_removal_test()
            all_results.extend(double_results)
            all_successful_combinations.extend(double_success)

            if not double_success:
                # 步骤3-5：多样本剔除测试
                for n in range(3, 6):  # 3, 4, 5样本剔除
                    multi_results, multi_success = self.multi_sample_removal_test(n)
                    all_results.extend(multi_results)
                    all_successful_combinations.extend(multi_success)

                    if multi_success:
                        self.logger.info(f"{n}样本剔除达到目标，停止进一步测试")
                        break

        # 生成结果总结
        self._generate_results_summary(all_results, all_successful_combinations)

        # 创建可视化
        self.create_comparison_visualization(all_results, all_successful_combinations)

        # 分析被剔除样本特征
        if all_successful_combinations:
            best_combo, best_result = self.analyze_removed_samples_characteristics(all_successful_combinations)

            # 评估剔除对模型性能的影响
            self._evaluate_model_performance_impact(all_results, best_result)

        return {
            'all_results': all_results,
            'successful_combinations': all_successful_combinations,
            'outlier_dates': self.outlier_dates
        }

    def _generate_results_summary(self, all_results, successful_combinations):
        """生成结果总结"""
        print("\n" + "="*100)
        print("逐步剔除回归分析结果总结")
        print("="*100)

        # 基本统计
        total_tests = len(all_results) - 1  # 减去基准模型
        successful_tests = len(successful_combinations)

        print(f"\n测试总数: {total_tests}")
        print(f"成功转正数: {successful_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "成功率: 0%")

        if successful_combinations:
            # 找到最小剔除数量
            min_removal_count = min(len(combo[0]) for combo in successful_combinations)
            min_removal_combos = [combo for combo in successful_combinations if len(combo[0]) == min_removal_count]

            print(f"\n最小剔除样本数: {min_removal_count}")
            print(f"达到最小剔除数的方案数: {len(min_removal_combos)}")

            # 显示所有成功方案
            print(f"\n所有成功方案:")
            for i, (combo, result) in enumerate(successful_combinations):
                combo_str = " + ".join(combo)
                print(f"  方案{i+1}: 剔除 {combo_str}")
                print(f"    剩余样本: {result['sample_size']}, 系数: {result['price_coefficient']:.6f}, "
                      f"p值: {result['price_pvalue']:.4f}, R²: {result['r_squared']:.4f}")
        else:
            print("\n❌ 没有找到能使系数转正的剔除方案")
            print("建议：")
            print("  1. 检查数据质量和模型规格")
            print("  2. 考虑非线性模型或其他变换")
            print("  3. 收集更多数据以增加样本量")

    def _evaluate_model_performance_impact(self, all_results, best_result):
        """评估剔除样本对模型整体性能的影响"""
        print("\n" + "="*80)
        print("模型性能影响评估")
        print("="*80)

        baseline_result = all_results[0]  # 基准模型

        # 性能对比
        sample_reduction = baseline_result['sample_size'] - best_result['sample_size']
        sample_reduction_pct = sample_reduction / baseline_result['sample_size'] * 100

        r2_change = best_result['r_squared'] - baseline_result['r_squared']
        coef_change = best_result['price_coefficient'] - baseline_result['price_coefficient']

        print(f"样本数变化: {baseline_result['sample_size']} → {best_result['sample_size']} "
              f"(减少 {sample_reduction} 个, {sample_reduction_pct:.1f}%)")
        print(f"R²变化: {baseline_result['r_squared']:.4f} → {best_result['r_squared']:.4f} "
              f"({'+'if r2_change >= 0 else ''}{r2_change:.4f})")
        print(f"价格比系数变化: {baseline_result['price_coefficient']:.6f} → {best_result['price_coefficient']:.6f} "
              f"({'+'if coef_change >= 0 else ''}{coef_change:.6f})")

        # 统计显著性变化
        baseline_sig = "显著" if baseline_result['price_pvalue'] < 0.05 else "不显著"
        best_sig = "显著" if best_result['price_pvalue'] < 0.05 else "不显著"
        print(f"统计显著性: {baseline_sig} (p={baseline_result['price_pvalue']:.4f}) → "
              f"{best_sig} (p={best_result['price_pvalue']:.4f})")

        # 经济意义评估
        print(f"\n经济意义评估:")
        print(f"  ✓ 系数符号: 从负值转为正值，符合经济直觉")
        print(f"  ✓ 系数大小: {best_result['price_coefficient']:.6f} (合理范围)")

        if best_result['price_pvalue'] < 0.05:
            print(f"  ✓ 统计显著性: p={best_result['price_pvalue']:.4f} < 0.05")
        else:
            print(f"  ⚠ 统计显著性: p={best_result['price_pvalue']:.4f} ≥ 0.05 (边际显著)")

        if r2_change >= 0:
            print(f"  ✓ 模型拟合度: R²提升 {r2_change:.4f}")
        else:
            print(f"  ⚠ 模型拟合度: R²下降 {abs(r2_change):.4f}")

        # 建议
        print(f"\n建议:")
        if sample_reduction_pct < 20:
            print(f"  ✓ 样本损失较小 ({sample_reduction_pct:.1f}%)，剔除方案可接受")
        else:
            print(f"  ⚠ 样本损失较大 ({sample_reduction_pct:.1f}%)，需谨慎使用")

        if best_result['price_pvalue'] < 0.05 and r2_change >= -0.05:
            print(f"  ✅ 推荐使用剔除后的模型")
        else:
            print(f"  ⚠ 建议进一步验证剔除方案的合理性")


if __name__ == "__main__":
    # 运行逐步剔除回归分析
    analyzer = StepwiseOutlierRemovalAnalysis()
    results = analyzer.run_comprehensive_stepwise_analysis()

    if results:
        print("\n" + "="*80)
        print("✓ 逐步剔除回归分析完成")
        print("详细结果图表已保存到 output/stepwise_outlier_removal_analysis.png")
        print("="*80)
    else:
        print("✗ 逐步剔除回归分析失败")
