"""
成都项目数据处理模块
负责Excel文件读取、数据清洗、验证和数据库操作
"""

import pandas as pd
import sqlite3
import numpy as np
from pathlib import Path
from typing import Optional, Tuple, Dict, Any
from contextlib import contextmanager

from config import (
    DATA_CONFIG, DATABASE_CONFIG, COLUMN_MAPPING, 
    VALIDATION_CONFIG, OUTPUT_CONFIG
)
from utils import ChengduAnalysisLogger, DataValidator, ensure_directory_exists


class ExcelDataReader:
    """Excel数据读取器"""
    
    def __init__(self, logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化Excel数据读取器
        
        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or ChengduAnalysisLogger("ExcelDataReader")
        self.validator = DataValidator(self.logger)
    
    def read_excel_file(self, file_path: Optional[Path] = None) -> Optional[pd.DataFrame]:
        """
        读取Excel文件并进行基础数据处理
        
        Args:
            file_path: Excel文件路径，默认使用配置中的路径
            
        Returns:
            Optional[pd.DataFrame]: 读取成功返回DataFrame，失败返回None
        """
        file_path = file_path or DATA_CONFIG['excel_file_path']
        
        try:
            self.logger.info(f"开始读取Excel文件: {file_path}")
            
            # 检查文件是否存在
            if not file_path.exists():
                self.logger.error(f"Excel文件不存在: {file_path}")
                return None
            
            # 读取Excel文件
            df = pd.read_excel(file_path)
            self.logger.info(f"✓ 成功读取Excel文件，数据形状: {df.shape}")
            
            # 数据结构验证
            structure_valid, structure_errors = self.validator.validate_dataframe_structure(df)
            if not structure_valid:
                self.logger.error("数据结构验证失败")
                for error in structure_errors:
                    self.logger.error(f"  - {error}")
                return None
            
            # 数据类型处理和验证
            df_processed = self._process_data_types(df)
            if df_processed is None:
                return None
            
            # 列名映射
            df_mapped = self._map_column_names(df_processed)
            
            self.logger.info("✓ Excel数据读取和处理完成")
            return df_mapped
            
        except Exception as e:
            self.logger.error(f"读取Excel文件时发生错误: {str(e)}")
            return None
    
    def _process_data_types(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        处理和转换数据类型
        
        Args:
            df: 原始DataFrame
            
        Returns:
            Optional[pd.DataFrame]: 处理后的DataFrame
        """
        try:
            df_copy = df.copy()
            
            # 处理日期列
            for col in VALIDATION_CONFIG['date_columns']:
                if col in df_copy.columns:
                    df_copy[col] = pd.to_datetime(df_copy[col], errors='coerce')
                    invalid_dates = df_copy[col].isnull().sum()
                    if invalid_dates > 0:
                        self.logger.warning(f"列 '{col}' 中有 {invalid_dates} 个无效日期值")
            
            # 处理数值列
            for col in VALIDATION_CONFIG['numeric_columns']:
                if col in df_copy.columns:
                    df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
                    invalid_numbers = df_copy[col].isnull().sum()
                    if invalid_numbers > 0:
                        self.logger.warning(f"列 '{col}' 中有 {invalid_numbers} 个无效数值")
            
            # 验证处理后的数据类型
            type_valid, type_errors = self.validator.validate_data_types(df_copy)
            if not type_valid:
                self.logger.error("数据类型验证失败")
                for error in type_errors:
                    self.logger.error(f"  - {error}")
                return None
            
            # 检查缺失值
            missing_counts = df_copy.isnull().sum()
            total_missing = missing_counts.sum()
            
            if total_missing > 0:
                self.logger.warning(f"数据中共有 {total_missing} 个缺失值")
                for col, count in missing_counts[missing_counts > 0].items():
                    missing_ratio = count / len(df_copy)
                    self.logger.warning(f"  - 列 '{col}': {count} 个缺失值 ({missing_ratio:.2%})")
                    
                    if missing_ratio > VALIDATION_CONFIG['max_missing_ratio']:
                        self.logger.error(f"列 '{col}' 缺失值比例 {missing_ratio:.2%} 超过阈值 {VALIDATION_CONFIG['max_missing_ratio']:.2%}")
                        return None
            
            self.logger.info("✓ 数据类型处理完成")
            return df_copy
            
        except Exception as e:
            self.logger.error(f"数据类型处理时发生错误: {str(e)}")
            return None
    
    def _map_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        将中文列名映射为英文字段名
        
        Args:
            df: 原始DataFrame
            
        Returns:
            pd.DataFrame: 列名映射后的DataFrame
        """
        try:
            df_mapped = df.rename(columns=COLUMN_MAPPING)
            self.logger.info("✓ 列名映射完成")
            
            # 记录映射详情
            for chinese_name, english_name in COLUMN_MAPPING.items():
                if chinese_name in df.columns:
                    self.logger.debug(f"  映射: '{chinese_name}' -> '{english_name}'")
            
            return df_mapped
            
        except Exception as e:
            self.logger.error(f"列名映射时发生错误: {str(e)}")
            return df


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化数据库管理器
        
        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or ChengduAnalysisLogger("DatabaseManager")
        self.db_path = DATA_CONFIG['database_path']
        self.table_name = DATABASE_CONFIG['table_name']
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接对象
        """
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=DATABASE_CONFIG['connection_timeout']
            )
            
            if DATABASE_CONFIG['enable_foreign_keys']:
                conn.execute("PRAGMA foreign_keys = ON")
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作错误: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def create_table(self) -> bool:
        """
        创建数据表
        
        Returns:
            bool: 创建是否成功
        """
        try:
            with self.get_connection() as conn:
                # 创建表的SQL语句
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE NOT NULL,
                    ln_total_visitors REAL NOT NULL,
                    logit_night_ratio REAL NOT NULL,
                    avg_customer_spending REAL NOT NULL,
                    price_ratio REAL NOT NULL,
                    monday INTEGER NOT NULL,
                    tuesday INTEGER NOT NULL,
                    wednesday INTEGER NOT NULL,
                    thursday INTEGER NOT NULL,
                    friday INTEGER NOT NULL,
                    saturday INTEGER NOT NULL,
                    holiday_day_idx_c REAL NOT NULL,
                    holiday_day_idx_c_sq REAL NOT NULL,
                    is_2023 INTEGER NOT NULL,
                    is_2024 INTEGER NOT NULL,
                    temperature_c REAL NOT NULL,
                    temperature_c_sq REAL NOT NULL,
                    precipitation REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                
                conn.execute(create_table_sql)
                conn.commit()
                
                self.logger.info(f"✓ 数据表 '{self.table_name}' 创建成功")
                return True
                
        except Exception as e:
            self.logger.error(f"创建数据表失败: {str(e)}")
            return False

    def import_data(self, df: pd.DataFrame, replace_existing: bool = True) -> bool:
        """
        将DataFrame数据导入数据库

        Args:
            df: 要导入的DataFrame
            replace_existing: 是否替换现有数据

        Returns:
            bool: 导入是否成功
        """
        try:
            with self.get_connection() as conn:
                # 如果需要替换现有数据，先清空表
                if replace_existing:
                    conn.execute(f"DELETE FROM {self.table_name}")
                    self.logger.info(f"✓ 清空现有数据表 '{self.table_name}'")

                # 准备数据导入
                df_import = df.copy()

                # 确保所有必需列都存在
                required_columns = [
                    'date', 'ln_total_visitors', 'logit_night_ratio', 'avg_customer_spending',
                    'price_ratio', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday',
                    'saturday', 'holiday_day_idx_c', 'holiday_day_idx_c_sq', 'is_2023',
                    'is_2024', 'temperature_c', 'temperature_c_sq', 'precipitation'
                ]

                missing_columns = [col for col in required_columns if col not in df_import.columns]
                if missing_columns:
                    self.logger.error(f"DataFrame缺少必需列: {missing_columns}")
                    return False

                # 选择需要导入的列
                df_import = df_import[required_columns]

                # 导入数据
                rows_imported = df_import.to_sql(
                    self.table_name,
                    conn,
                    if_exists='append',
                    index=False,
                    method='multi'
                )

                conn.commit()
                self.logger.info(f"✓ 成功导入 {len(df_import)} 行数据到表 '{self.table_name}'")
                return True

        except Exception as e:
            self.logger.error(f"数据导入失败: {str(e)}")
            return False

    def get_data(self, query: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        从数据库获取数据

        Args:
            query: 自定义SQL查询，默认获取所有数据

        Returns:
            Optional[pd.DataFrame]: 查询结果DataFrame
        """
        try:
            with self.get_connection() as conn:
                if query is None:
                    query = f"SELECT * FROM {self.table_name} ORDER BY date"

                df = pd.read_sql_query(query, conn)
                self.logger.info(f"✓ 从数据库获取 {len(df)} 行数据")
                return df

        except Exception as e:
            self.logger.error(f"数据查询失败: {str(e)}")
            return None

    def get_table_info(self) -> Dict[str, Any]:
        """
        获取数据表信息

        Returns:
            Dict[str, Any]: 表信息字典
        """
        try:
            with self.get_connection() as conn:
                # 获取表结构信息
                cursor = conn.execute(f"PRAGMA table_info({self.table_name})")
                columns_info = cursor.fetchall()

                # 获取数据行数
                cursor = conn.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                row_count = cursor.fetchone()[0]

                # 获取数据范围
                cursor = conn.execute(f"SELECT MIN(date), MAX(date) FROM {self.table_name}")
                date_range = cursor.fetchone()

                table_info = {
                    'table_name': self.table_name,
                    'columns': [{'name': col[1], 'type': col[2], 'not_null': bool(col[3])} for col in columns_info],
                    'row_count': row_count,
                    'date_range': {
                        'start_date': date_range[0],
                        'end_date': date_range[1]
                    }
                }

                self.logger.info(f"✓ 获取表信息成功: {row_count} 行数据")
                return table_info

        except Exception as e:
            self.logger.error(f"获取表信息失败: {str(e)}")
            return {}


class DataProcessor:
    """数据处理主类，整合Excel读取和数据库操作"""

    def __init__(self, logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化数据处理器

        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or ChengduAnalysisLogger("DataProcessor")
        self.excel_reader = ExcelDataReader(self.logger)
        self.db_manager = DatabaseManager(self.logger)

    def process_excel_to_database(self, excel_path: Optional[Path] = None) -> bool:
        """
        完整的Excel到数据库处理流程

        Args:
            excel_path: Excel文件路径

        Returns:
            bool: 处理是否成功
        """
        try:
            self.logger.info("开始Excel到数据库的完整处理流程")

            # 1. 读取Excel文件
            df = self.excel_reader.read_excel_file(excel_path)
            if df is None:
                self.logger.error("Excel文件读取失败，终止处理流程")
                return False

            # 2. 创建数据库表
            if not self.db_manager.create_table():
                self.logger.error("数据库表创建失败，终止处理流程")
                return False

            # 3. 导入数据
            if not self.db_manager.import_data(df):
                self.logger.error("数据导入失败，终止处理流程")
                return False

            # 4. 验证导入结果
            table_info = self.db_manager.get_table_info()
            if table_info and table_info.get('row_count', 0) > 0:
                self.logger.info(f"✓ 数据处理完成，共导入 {table_info['row_count']} 行数据")
                self.logger.info(f"  数据时间范围: {table_info['date_range']['start_date']} 到 {table_info['date_range']['end_date']}")
                return True
            else:
                self.logger.error("数据验证失败，没有成功导入数据")
                return False

        except Exception as e:
            self.logger.error(f"数据处理流程发生错误: {str(e)}")
            return False

    def get_processed_data(self) -> Optional[pd.DataFrame]:
        """
        获取处理后的数据用于分析

        Returns:
            Optional[pd.DataFrame]: 处理后的数据
        """
        return self.db_manager.get_data()


if __name__ == "__main__":
    # 测试数据处理功能
    from config import create_directories

    # 创建必要目录
    create_directories()

    # 初始化数据处理器
    processor = DataProcessor()

    # 执行完整处理流程
    success = processor.process_excel_to_database()

    if success:
        print("✓ 数据处理模块测试完成")

        # 获取处理后的数据进行验证
        df = processor.get_processed_data()
        if df is not None:
            print(f"✓ 数据验证成功，数据形状: {df.shape}")
            print(f"✓ 数据列名: {list(df.columns)}")
    else:
        print("✗ 数据处理模块测试失败")
