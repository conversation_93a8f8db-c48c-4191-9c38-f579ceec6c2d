"""
成都项目结果输出和报告模块
负责生成回归分析结果报告、可视化图表和模型诊断
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

from config import DATA_CONFIG, OUTPUT_CONFIG
from utils import ChengduAnalysisLogger, ensure_directory_exists, get_current_timestamp


class ResultsReporter:
    """结果报告生成器"""
    
    def __init__(self, logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化结果报告生成器
        
        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or ChengduAnalysisLogger("ResultsReporter")
        self.output_dir = DATA_CONFIG['output_dir']
        
        # 确保输出目录存在
        ensure_directory_exists(self.output_dir)
        
        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.logger.info("结果报告生成器初始化完成")
    
    def generate_comprehensive_report(self, analysis_results: Dict[str, Any]) -> bool:
        """
        生成综合分析报告
        
        Args:
            analysis_results: 完整的分析结果字典
            
        Returns:
            bool: 报告生成是否成功
        """
        try:
            self.logger.info("开始生成综合分析报告")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 1. 生成文本报告
            text_report_success = self._generate_text_report(analysis_results, timestamp)
            
            # 2. 生成Excel报告
            excel_report_success = self._generate_excel_report(analysis_results, timestamp)
            
            # 3. 生成JSON报告（详细数据）
            json_report_success = self._generate_json_report(analysis_results, timestamp)
            
            # 4. 生成可视化图表
            visualization_success = self._generate_visualizations(analysis_results, timestamp)
            
            success_count = sum([text_report_success, excel_report_success, 
                               json_report_success, visualization_success])
            
            self.logger.info(f"✓ 综合报告生成完成，成功生成 {success_count}/4 个报告组件")
            return success_count >= 3  # 至少3个组件成功才算成功
            
        except Exception as e:
            self.logger.error(f"生成综合报告失败: {str(e)}")
            return False
    
    def _generate_text_report(self, results: Dict[str, Any], timestamp: str) -> bool:
        """生成文本格式的分析报告"""
        try:
            report_file = self.output_dir / f"regression_analysis_report_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("成都项目暑期数据回归分析报告\n")
                f.write("=" * 80 + "\n")
                f.write(f"生成时间: {results.get('analysis_timestamp', get_current_timestamp())}\n")
                f.write(f"数据观测数: {results.get('data_summary', {}).get('total_observations', 'N/A')}\n")
                
                date_range = results.get('data_summary', {}).get('date_range', {})
                if date_range.get('start_date') and date_range.get('end_date'):
                    f.write(f"数据时间范围: {date_range['start_date']} 至 {date_range['end_date']}\n")
                
                f.write("\n")
                
                # 分析摘要
                summary = results.get('analysis_summary', {})
                f.write("分析摘要\n")
                f.write("-" * 40 + "\n")
                f.write(f"成功分析模型数: {summary.get('total_models', 0)}\n")
                f.write(f"成功模型列表: {', '.join(summary.get('successful_models', []))}\n")
                f.write("\n")
                
                # 模型详细结果
                for model_name, model_results in results.get('models', {}).items():
                    f.write(f"模型: {model_name}\n")
                    f.write("=" * 60 + "\n")
                    
                    # 基本信息
                    f.write(f"因变量: {model_results.get('dependent_variable', 'N/A')}\n")
                    f.write(f"样本数: {model_results.get('sample_size', 'N/A')}\n")
                    f.write("\n")
                    
                    # 模型拟合优度
                    model_fit = model_results.get('model_fit', {})
                    f.write("模型拟合优度:\n")
                    f.write(f"  R² = {model_fit.get('r_squared', 'N/A'):.4f}\n")
                    f.write(f"  调整R² = {model_fit.get('adjusted_r_squared', 'N/A'):.4f}\n")
                    f.write(f"  F统计量 = {model_fit.get('f_statistic', 'N/A'):.4f}\n")
                    f.write(f"  F检验p值 = {model_fit.get('f_pvalue', 'N/A'):.4f}\n")
                    f.write(f"  AIC = {model_fit.get('aic', 'N/A'):.4f}\n")
                    f.write(f"  BIC = {model_fit.get('bic', 'N/A'):.4f}\n")
                    f.write("\n")
                    
                    # 回归系数
                    coefficients = model_results.get('coefficients', {})
                    f.write("回归系数:\n")
                    f.write(f"{'变量':<20} {'系数':<12} {'标准误':<12} {'t值':<10} {'p值':<10} {'显著性':<8}\n")
                    f.write("-" * 80 + "\n")
                    
                    coef_values = coefficients.get('values', {})
                    std_errors = coefficients.get('std_errors', {})
                    t_values = coefficients.get('t_values', {})
                    p_values = coefficients.get('p_values', {})
                    
                    for var in coef_values.keys():
                        coef = coef_values.get(var, 0)
                        std_err = std_errors.get(var, 0)
                        t_val = t_values.get(var, 0)
                        p_val = p_values.get(var, 1)
                        
                        # 显著性标记
                        if p_val < 0.001:
                            sig = "***"
                        elif p_val < 0.01:
                            sig = "**"
                        elif p_val < 0.05:
                            sig = "*"
                        elif p_val < 0.1:
                            sig = "."
                        else:
                            sig = ""
                        
                        f.write(f"{var:<20} {coef:<12.4f} {std_err:<12.4f} {t_val:<10.4f} {p_val:<10.4f} {sig:<8}\n")
                    
                    f.write("\n显著性代码: 0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n")
                    f.write("\n" + "=" * 60 + "\n\n")
                
                # 模型比较
                f.write("模型比较\n")
                f.write("-" * 40 + "\n")
                comparison = summary.get('model_comparison', {})
                if comparison:
                    f.write(f"{'模型':<30} {'R²':<10} {'调整R²':<10} {'AIC':<10} {'F检验p值':<12}\n")
                    f.write("-" * 80 + "\n")
                    for model_name, metrics in comparison.items():
                        f.write(f"{model_name:<30} {metrics.get('r_squared', 0):<10.4f} "
                               f"{metrics.get('adjusted_r_squared', 0):<10.4f} "
                               f"{metrics.get('aic', 0):<10.4f} {metrics.get('f_pvalue', 1):<12.4f}\n")
                
                f.write("\n" + "=" * 80 + "\n")
                f.write("报告生成完成\n")
            
            self.logger.info(f"✓ 文本报告已保存: {report_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成文本报告失败: {str(e)}")
            return False
    
    def _generate_excel_report(self, results: Dict[str, Any], timestamp: str) -> bool:
        """生成Excel格式的分析报告"""
        try:
            report_file = self.output_dir / f"regression_analysis_results_{timestamp}.xlsx"
            
            with pd.ExcelWriter(report_file, engine='openpyxl') as writer:
                # 1. 分析摘要工作表
                summary_data = []
                for model_name, model_results in results.get('models', {}).items():
                    model_fit = model_results.get('model_fit', {})
                    summary_data.append({
                        '模型名称': model_name,
                        '因变量': model_results.get('dependent_variable', ''),
                        '样本数': model_results.get('sample_size', 0),
                        'R²': model_fit.get('r_squared', 0),
                        '调整R²': model_fit.get('adjusted_r_squared', 0),
                        'F统计量': model_fit.get('f_statistic', 0),
                        'F检验p值': model_fit.get('f_pvalue', 1),
                        'AIC': model_fit.get('aic', 0),
                        'BIC': model_fit.get('bic', 0)
                    })
                
                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='分析摘要', index=False)
                
                # 2. 每个模型的详细结果
                for model_name, model_results in results.get('models', {}).items():
                    # 回归系数表
                    coefficients = model_results.get('coefficients', {})
                    coef_data = []
                    
                    coef_values = coefficients.get('values', {})
                    std_errors = coefficients.get('std_errors', {})
                    t_values = coefficients.get('t_values', {})
                    p_values = coefficients.get('p_values', {})
                    conf_intervals = coefficients.get('confidence_intervals', {})
                    
                    for var in coef_values.keys():
                        ci = conf_intervals.get(var, (None, None))
                        coef_data.append({
                            '变量': var,
                            '系数': coef_values.get(var, 0),
                            '标准误': std_errors.get(var, 0),
                            't值': t_values.get(var, 0),
                            'p值': p_values.get(var, 1),
                            '置信区间下限': ci[0] if ci[0] is not None else '',
                            '置信区间上限': ci[1] if ci[1] is not None else ''
                        })
                    
                    if coef_data:
                        coef_df = pd.DataFrame(coef_data)
                        sheet_name = f"{model_name}_系数"[:31]  # Excel工作表名称限制
                        coef_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.logger.info(f"✓ Excel报告已保存: {report_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"生成Excel报告失败: {str(e)}")
            return False

    def _generate_json_report(self, results: Dict[str, Any], timestamp: str) -> bool:
        """生成JSON格式的详细分析结果"""
        try:
            report_file = self.output_dir / f"regression_analysis_data_{timestamp}.json"

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"✓ JSON报告已保存: {report_file}")
            return True

        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {str(e)}")
            return False

    def _generate_visualizations(self, results: Dict[str, Any], timestamp: str) -> bool:
        """生成可视化图表"""
        try:
            self.logger.info("开始生成可视化图表")

            # 为每个模型生成诊断图表
            for model_name, model_results in results.get('models', {}).items():
                self._create_model_diagnostic_plots(model_name, model_results, timestamp)

            # 生成模型比较图表
            self._create_model_comparison_plots(results, timestamp)

            self.logger.info("✓ 可视化图表生成完成")
            return True

        except Exception as e:
            self.logger.error(f"生成可视化图表失败: {str(e)}")
            return False

    def _create_model_diagnostic_plots(self, model_name: str, model_results: Dict[str, Any], timestamp: str):
        """为单个模型创建诊断图表"""
        try:
            # 获取数据
            residuals = model_results.get('residuals', {}).get('residuals', [])
            fitted_values = model_results.get('predictions', {}).get('fitted_values', [])
            actual_values = model_results.get('predictions', {}).get('actual_values', [])

            if not all([residuals, fitted_values, actual_values]):
                self.logger.warning(f"模型 {model_name} 缺少绘图所需数据")
                return

            # 创建2x2的子图
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'模型诊断图表: {model_name}', fontsize=16, fontweight='bold')

            # 1. 残差vs拟合值图
            axes[0, 0].scatter(fitted_values, residuals, alpha=0.6)
            axes[0, 0].axhline(y=0, color='red', linestyle='--')
            axes[0, 0].set_xlabel('拟合值')
            axes[0, 0].set_ylabel('残差')
            axes[0, 0].set_title('残差 vs 拟合值')
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 实际值vs预测值图
            axes[0, 1].scatter(actual_values, fitted_values, alpha=0.6)
            min_val = min(min(actual_values), min(fitted_values))
            max_val = max(max(actual_values), max(fitted_values))
            axes[0, 1].plot([min_val, max_val], [min_val, max_val], 'red', linestyle='--')
            axes[0, 1].set_xlabel('实际值')
            axes[0, 1].set_ylabel('预测值')
            axes[0, 1].set_title('实际值 vs 预测值')
            axes[0, 1].grid(True, alpha=0.3)

            # 3. 残差直方图
            axes[1, 0].hist(residuals, bins=20, alpha=0.7, edgecolor='black')
            axes[1, 0].set_xlabel('残差')
            axes[1, 0].set_ylabel('频数')
            axes[1, 0].set_title('残差分布直方图')
            axes[1, 0].grid(True, alpha=0.3)

            # 4. Q-Q图（正态性检验）
            from scipy import stats
            stats.probplot(residuals, dist="norm", plot=axes[1, 1])
            axes[1, 1].set_title('残差Q-Q图（正态性检验）')
            axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            plot_file = self.output_dir / f"diagnostic_plots_{model_name}_{timestamp}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"✓ 模型 {model_name} 诊断图表已保存: {plot_file}")

        except Exception as e:
            self.logger.error(f"创建模型 {model_name} 诊断图表失败: {str(e)}")

    def _create_model_comparison_plots(self, results: Dict[str, Any], timestamp: str):
        """创建模型比较图表"""
        try:
            models_data = results.get('models', {})
            if len(models_data) < 2:
                self.logger.info("模型数量少于2个，跳过模型比较图表生成")
                return

            # 准备比较数据
            model_names = []
            r_squared_values = []
            adj_r_squared_values = []
            aic_values = []

            for model_name, model_results in models_data.items():
                model_fit = model_results.get('model_fit', {})
                model_names.append(model_name)
                r_squared_values.append(model_fit.get('r_squared', 0))
                adj_r_squared_values.append(model_fit.get('adjusted_r_squared', 0))
                aic_values.append(model_fit.get('aic', 0))

            # 创建比较图表
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            fig.suptitle('模型性能比较', fontsize=16, fontweight='bold')

            # R²比较
            x_pos = np.arange(len(model_names))
            axes[0].bar(x_pos, r_squared_values, alpha=0.7, color='skyblue', label='R²')
            axes[0].bar(x_pos, adj_r_squared_values, alpha=0.7, color='orange', label='调整R²')
            axes[0].set_xlabel('模型')
            axes[0].set_ylabel('R²值')
            axes[0].set_title('R²比较')
            axes[0].set_xticks(x_pos)
            axes[0].set_xticklabels(model_names, rotation=45)
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)

            # AIC比较（越小越好）
            axes[1].bar(x_pos, aic_values, alpha=0.7, color='lightcoral')
            axes[1].set_xlabel('模型')
            axes[1].set_ylabel('AIC值')
            axes[1].set_title('AIC比较（越小越好）')
            axes[1].set_xticks(x_pos)
            axes[1].set_xticklabels(model_names, rotation=45)
            axes[1].grid(True, alpha=0.3)

            # 综合评分（基于R²和AIC的标准化分数）
            # 标准化R²（越大越好）和AIC（越小越好）
            r_squared_norm = np.array(r_squared_values) / max(r_squared_values) if max(r_squared_values) > 0 else np.zeros(len(r_squared_values))
            aic_norm = min(aic_values) / np.array(aic_values) if min(aic_values) > 0 else np.zeros(len(aic_values))
            composite_score = (r_squared_norm + aic_norm) / 2

            axes[2].bar(x_pos, composite_score, alpha=0.7, color='lightgreen')
            axes[2].set_xlabel('模型')
            axes[2].set_ylabel('综合评分')
            axes[2].set_title('综合性能评分')
            axes[2].set_xticks(x_pos)
            axes[2].set_xticklabels(model_names, rotation=45)
            axes[2].grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            plot_file = self.output_dir / f"model_comparison_{timestamp}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"✓ 模型比较图表已保存: {plot_file}")

        except Exception as e:
            self.logger.error(f"创建模型比较图表失败: {str(e)}")

    def generate_summary_report(self, results: Dict[str, Any]) -> str:
        """
        生成简要的分析结果摘要

        Args:
            results: 分析结果字典

        Returns:
            str: 摘要文本
        """
        try:
            summary_lines = []
            summary_lines.append("=" * 60)
            summary_lines.append("成都项目回归分析结果摘要")
            summary_lines.append("=" * 60)

            # 基本信息
            data_summary = results.get('data_summary', {})
            summary_lines.append(f"分析时间: {results.get('analysis_timestamp', 'N/A')}")
            summary_lines.append(f"数据观测数: {data_summary.get('total_observations', 'N/A')}")

            date_range = data_summary.get('date_range', {})
            if date_range.get('start_date') and date_range.get('end_date'):
                summary_lines.append(f"数据时间范围: {date_range['start_date']} 至 {date_range['end_date']}")

            summary_lines.append("")

            # 模型结果摘要
            models = results.get('models', {})
            summary_lines.append(f"成功分析模型数: {len(models)}")
            summary_lines.append("")

            for model_name, model_results in models.items():
                model_fit = model_results.get('model_fit', {})
                summary_lines.append(f"模型: {model_name}")
                summary_lines.append(f"  因变量: {model_results.get('dependent_variable', 'N/A')}")
                summary_lines.append(f"  R² = {model_fit.get('r_squared', 0):.4f}")
                summary_lines.append(f"  调整R² = {model_fit.get('adjusted_r_squared', 0):.4f}")
                summary_lines.append(f"  F检验p值 = {model_fit.get('f_pvalue', 1):.4f}")

                # 显著变量数量
                p_values = model_results.get('coefficients', {}).get('p_values', {})
                significant_vars = sum(1 for var, p_val in p_values.items() if var != 'const' and p_val < 0.05)
                summary_lines.append(f"  显著变量数量 (p<0.05): {significant_vars}")
                summary_lines.append("")

            summary_lines.append("=" * 60)

            return "\n".join(summary_lines)

        except Exception as e:
            self.logger.error(f"生成摘要报告失败: {str(e)}")
            return "摘要报告生成失败"


if __name__ == "__main__":
    # 测试结果报告功能
    from regression_analysis import RegressionAnalysisManager
    from data_processor import DataProcessor

    # 获取数据并运行分析
    processor = DataProcessor()
    df = processor.get_processed_data()

    if df is not None:
        analyzer = RegressionAnalysisManager()
        results = analyzer.run_analysis(df)

        if results:
            # 生成报告
            reporter = ResultsReporter()
            success = reporter.generate_comprehensive_report(results)

            if success:
                print("✓ 结果报告模块测试完成")

                # 显示摘要
                summary = reporter.generate_summary_report(results)
                print("\n" + summary)
            else:
                print("✗ 结果报告生成失败")
        else:
            print("✗ 无法获取分析结果")
    else:
        print("✗ 无法获取数据进行测试")
