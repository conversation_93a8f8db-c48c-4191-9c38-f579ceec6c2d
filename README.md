# 成都项目暑期数据回归分析系统

## 项目概述

本项目是一个完整的数据分析系统，专门用于分析成都项目历年暑期数据。系统实现了从Excel数据读取、数据库存储到回归分析和结果输出的完整流程。

## 功能特性

### 1. 数据处理
- ✅ Excel文件读取和验证
- ✅ 数据类型自动转换和验证
- ✅ SQLite数据库存储
- ✅ 数据完整性检查

### 2. 回归分析
- ✅ **模型1**: ln总人次回归模型
- ✅ **模型2**: logit夜场人次占比回归模型
- ✅ 使用scikit-learn和statsmodels双重验证
- ✅ 完整的统计检验（t检验、F检验等）
- ✅ 模型诊断和残差分析

### 3. 结果输出
- ✅ 文本格式详细报告
- ✅ Excel格式结构化结果
- ✅ JSON格式原始数据
- ✅ 可视化诊断图表
- ✅ 模型比较分析

## 项目结构

```
chengdu_v2/
├── main.py                    # 主程序入口
├── config.py                  # 配置文件
├── utils.py                   # 工具函数和日志
├── data_processor.py          # 数据处理模块
├── regression_analysis.py     # 回归分析模块
├── results_reporter.py        # 结果输出模块
├── requirements.txt           # Python依赖包
├── README.md                  # 项目说明文档
├── data_chengdu_v2.xlsx      # 原始数据文件
├── chengdu_analysis.db       # SQLite数据库（运行后生成）
├── output/                   # 输出目录（运行后生成）
│   ├── *.txt                 # 文本报告
│   ├── *.xlsx                # Excel报告
│   ├── *.json                # JSON数据
│   └── *.png                 # 可视化图表
└── logs/                     # 日志目录（运行后生成）
    └── *.log                 # 运行日志
```

## 数据结构说明

### 变量定义

**因变量：**
- `ln总人次`: 对总人次（日票人次+夜票人次）取自然对数
- `logit夜场人次占比`: 对夜场人次占比（夜场人次/总人次）进行logit变换

**自变量：**
- **ln总人次模型专属**: 平均客单（总收入/总人次）
- **logit夜场人次占比模型专属**: 价格比（日票价格/夜票价格）
- **共同自变量**:
  - 星期几虚拟变量（6个，以星期日为基准）
  - 年份虚拟变量（Is_2023、Is_2024，以2025年为基准）
  - 温度_C（中心化）及其二次项
  - 降水量
  - Holiday_Day_Idx_C（假期天数，中心化）及其二次项

## 安装和使用

### 1. 环境准备

```bash
# 确保Python 3.8+已安装
python --version

# 安装依赖包
pip install -r requirements.txt
```

### 2. 数据准备

确保 `data_chengdu_v2.xlsx` 文件在项目根目录中，且包含以下列：
- 日期
- ln总人次
- logit夜场人次占比
- 平均客单
- 价格比
- 星期一、星期二、星期三、星期四、星期五、星期六
- Holiday_Day_Idx_C、Holiday_Day_Idx_C_Sq
- Is_2023、Is_2024
- 温度_C、温度_C_Sq
- 降水量

### 3. 运行分析

#### 完整分析流程（推荐）
```bash
python main.py
```

#### 其他命令
```bash
# 检查数据状态
python main.py check

# 仅运行回归分析（需要数据已在数据库中）
python main.py analysis

# 显示帮助信息
python main.py help
```

## 输出结果说明

### 1. 文本报告 (`regression_analysis_report_*.txt`)
- 完整的回归分析结果
- 系数表格和显著性检验
- 模型拟合优度指标
- 模型比较

### 2. Excel报告 (`regression_analysis_results_*.xlsx`)
- 分析摘要工作表
- 各模型系数详细表
- 便于进一步分析的结构化数据

### 3. JSON数据 (`regression_analysis_data_*.json`)
- 完整的原始分析结果
- 包含所有计算数据
- 便于程序化处理

### 4. 可视化图表
- `diagnostic_plots_*.png`: 模型诊断图表
  - 残差vs拟合值图
  - 实际值vs预测值图
  - 残差分布直方图
  - Q-Q正态性检验图
- `model_comparison_*.png`: 模型比较图表

## 回归模型详情

### 模型1：ln总人次回归模型
```
ln总人次 = β₀ + β₁×平均客单 + β₂×星期一 + ... + β₁₇×降水量 + ε
```

### 模型2：logit夜场人次占比回归模型
```
logit夜场人次占比 = γ₀ + γ₁×价格比 + γ₂×星期一 + ... + γ₁₇×降水量 + ε
```

## 技术特性

- **模块化设计**: 清晰的代码结构，便于维护和扩展
- **错误处理**: 完善的异常处理和日志记录
- **数据验证**: 多层次的数据完整性检查
- **统计严谨**: 使用statsmodels进行专业统计分析
- **结果丰富**: 多格式输出，满足不同需求

## 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 检查文件路径是否正确
   - 确认文件格式为.xlsx
   - 验证文件是否包含所有必需列

2. **数据库连接错误**
   - 检查文件权限
   - 确保有足够的磁盘空间

3. **回归分析失败**
   - 检查数据中是否有缺失值
   - 验证数值列的数据类型
   - 确认样本数量足够

4. **可视化图表生成失败**
   - 检查matplotlib中文字体支持
   - 确认有足够的内存

### 日志查看

所有运行日志保存在 `logs/` 目录中，包含详细的错误信息和调试信息。

## 开发信息

- **开发语言**: Python 3.8+
- **主要依赖**: pandas, numpy, scikit-learn, statsmodels, matplotlib
- **数据库**: SQLite
- **代码风格**: PEP 8

## 许可证

本项目仅用于成都项目数据分析，请勿用于其他商业用途。
