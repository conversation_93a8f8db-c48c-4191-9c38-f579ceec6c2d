2025-08-01 16:38:26,006 - StepwiseOutlierRemoval - INFO - 逐步剔除异常样本分析器初始化完成
2025-08-01 16:38:26,006 - StepwiseOutlierRemoval - INFO - 目标异常样本: ['2025-07-03', '2025-06-28', '2025-07-19', '2025-07-05', '2025-07-26']
2025-08-01 16:38:26,006 - StepwiseOutlierRemoval - INFO - 开始运行逐步剔除异常样本综合分析
2025-08-01 16:38:26,009 - DataProcessor - INFO - ✓ 从数据库获取 164 行数据
2025-08-01 16:38:26,011 - StepwiseOutlierRemoval - INFO - 数据加载成功，总样本数: 164
2025-08-01 16:38:26,011 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:38:26,016 - StepwiseOutlierRemoval - INFO - 单样本剔除测试
2025-08-01 16:38:26,016 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:38:26,049 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:38:26,050 - StepwiseOutlierRemoval - INFO - 双样本剔除测试
2025-08-01 16:38:26,057 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:38:26,071 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-03 + 2025-06-28
2025-08-01 16:38:26,080 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-03 + 2025-07-19
2025-08-01 16:38:26,085 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-03 + 2025-07-05
2025-08-01 16:38:26,097 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-03 + 2025-07-26
2025-08-01 16:38:26,101 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-06-28 + 2025-07-19
2025-08-01 16:38:26,105 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-06-28 + 2025-07-05
2025-08-01 16:38:26,110 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-06-28 + 2025-07-26
2025-08-01 16:38:26,115 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-19 + 2025-07-05
2025-08-01 16:38:26,120 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-19 + 2025-07-26
2025-08-01 16:38:26,123 - StepwiseOutlierRemoval - INFO - 🎯 找到最优双样本组合: 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,124 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:38:26,124 - StepwiseOutlierRemoval - INFO - 多样本剔除测试
2025-08-01 16:38:26,125 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:38:26,132 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-03 + 2025-06-28 + 2025-07-19
2025-08-01 16:38:26,138 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-03 + 2025-06-28 + 2025-07-05
2025-08-01 16:38:26,140 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-03 + 2025-06-28 + 2025-07-26
2025-08-01 16:38:26,145 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-03 + 2025-07-19 + 2025-07-05
2025-08-01 16:38:26,148 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-03 + 2025-07-19 + 2025-07-26
2025-08-01 16:38:26,154 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-03 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,159 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-06-28 + 2025-07-19 + 2025-07-05
2025-08-01 16:38:26,163 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-06-28 + 2025-07-19 + 2025-07-26
2025-08-01 16:38:26,167 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-06-28 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,171 - StepwiseOutlierRemoval - INFO - 🎯 找到最优3样本组合: 2025-07-19 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,172 - StepwiseOutlierRemoval - INFO - 在3样本剔除中找到10个有效组合
2025-08-01 16:38:26,183 - StepwiseOutlierRemoval - INFO - 🎯 找到最优4样本组合: 2025-07-03 + 2025-06-28 + 2025-07-19 + 2025-07-05
2025-08-01 16:38:26,186 - StepwiseOutlierRemoval - INFO - 🎯 找到最优4样本组合: 2025-07-03 + 2025-06-28 + 2025-07-19 + 2025-07-26
2025-08-01 16:38:26,195 - StepwiseOutlierRemoval - INFO - 🎯 找到最优4样本组合: 2025-07-03 + 2025-06-28 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,198 - StepwiseOutlierRemoval - INFO - 🎯 找到最优4样本组合: 2025-07-03 + 2025-07-19 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,203 - StepwiseOutlierRemoval - INFO - 🎯 找到最优4样本组合: 2025-06-28 + 2025-07-19 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,204 - StepwiseOutlierRemoval - INFO - 在4样本剔除中找到5个有效组合
2025-08-01 16:38:26,209 - StepwiseOutlierRemoval - INFO - 🎯 找到最优5样本组合: 2025-07-03 + 2025-06-28 + 2025-07-19 + 2025-07-05 + 2025-07-26
2025-08-01 16:38:26,209 - StepwiseOutlierRemoval - INFO - 在5样本剔除中找到1个有效组合
2025-08-01 16:38:26,210 - StepwiseOutlierRemoval - INFO - 创建可视化图表
2025-08-01 16:39:05,021 - StepwiseOutlierRemoval - INFO - ================================================================================
2025-08-01 16:39:05,021 - StepwiseOutlierRemoval - INFO - 分析最优解决方案
2025-08-01 16:39:05,021 - StepwiseOutlierRemoval - INFO - ================================================================================
