"""
logit夜场人次占比模型改进分析
基于ln总人次模型交互项成功经验，为logit模型实施类似改进
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.api as sm
from data_processor import DataProcessor
from utils import ChengduAnalysisLogger

class LogitModelImprovement:
    """logit夜场人次占比模型改进分析类"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = ChengduAnalysisLogger("LogitModelImprovement")
        self.processor = DataProcessor()
        self.df = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.logger.info("logit夜场人次占比模型改进分析器初始化完成")
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        self.df = self.processor.get_processed_data()
        if self.df is None:
            self.logger.error("无法加载数据")
            return False
        
        # 创建价格比交互项
        self._create_price_ratio_interactions()
        return True
    
    def _create_price_ratio_interactions(self):
        """创建价格比与年份的交互项"""
        # 1. 价格比与年份的交互项
        self.df['price_ratio_x_2023'] = self.df['price_ratio'] * self.df['is_2023']
        self.df['price_ratio_x_2024'] = self.df['price_ratio'] * self.df['is_2024']
        
        # 2. 计算各年份价格比的基本统计信息
        for year in [2023, 2024, 2025]:
            if year == 2023:
                year_mask = self.df['is_2023'] == 1
            elif year == 2024:
                year_mask = self.df['is_2024'] == 1
            else:
                year_mask = (self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)
            
            if year_mask.sum() > 0:
                year_mean = self.df.loc[year_mask, 'price_ratio'].mean()
                year_std = self.df.loc[year_mask, 'price_ratio'].std()
                self.logger.info(f"{year}年价格比 - 均值: {year_mean:.4f}, 标准差: {year_std:.4f}")
        
        self.logger.info("价格比交互项创建完成")
    
    def compare_models(self):
        """比较原始模型与改进模型"""
        self.logger.info("开始比较原始模型与改进模型")
        
        # 基础变量（除了价格比相关变量）
        base_vars = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                    'is_2023', 'is_2024', 'temperature_c', 'temperature_c_sq',
                    'precipitation', 'holiday_day_idx_c', 'holiday_day_idx_c_sq']
        
        # 定义模型
        models = {
            '原始logit模型': base_vars + ['price_ratio'],
            '交互项logit模型': base_vars + ['price_ratio', 'price_ratio_x_2023', 'price_ratio_x_2024']
        }
        
        results = {}
        
        for model_name, variables in models.items():
            try:
                # 准备数据
                X = self.df[variables].dropna()
                y = self.df.loc[X.index, 'logit_night_ratio']
                X_with_const = sm.add_constant(X)
                
                # 拟合模型
                model = sm.OLS(y, X_with_const).fit()
                
                # 提取价格比相关系数
                price_coef = model.params.get('price_ratio', None)
                price_pvalue = model.pvalues.get('price_ratio', None)
                
                # 提取交互项系数（如果存在）
                interaction_2023_coef = model.params.get('price_ratio_x_2023', None)
                interaction_2023_pvalue = model.pvalues.get('price_ratio_x_2023', None)
                interaction_2024_coef = model.params.get('price_ratio_x_2024', None)
                interaction_2024_pvalue = model.pvalues.get('price_ratio_x_2024', None)
                
                results[model_name] = {
                    'model_object': model,
                    'r_squared': model.rsquared,
                    'adj_r_squared': model.rsquared_adj,
                    'aic': model.aic,
                    'bic': model.bic,
                    'f_pvalue': model.f_pvalue,
                    'price_ratio_coef': price_coef,
                    'price_ratio_pvalue': price_pvalue,
                    'interaction_2023_coef': interaction_2023_coef,
                    'interaction_2023_pvalue': interaction_2023_pvalue,
                    'interaction_2024_coef': interaction_2024_coef,
                    'interaction_2024_pvalue': interaction_2024_pvalue,
                    'sample_size': len(X)
                }
                
                self.logger.info(f"{model_name} - R²: {model.rsquared:.4f}, 价格比系数: {price_coef:.6f}")
                
            except Exception as e:
                self.logger.error(f"模型 {model_name} 拟合失败: {e}")
                results[model_name] = None
        
        return results
    
    def analyze_by_year(self):
        """按年份分别分析价格比效应"""
        self.logger.info("开始按年份分别分析价格比效应")
        
        # 基础变量（不包含年份虚拟变量）
        base_vars = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                    'temperature_c', 'temperature_c_sq', 'precipitation', 
                    'holiday_day_idx_c', 'holiday_day_idx_c_sq']
        
        year_results = {}
        
        for year in [2023, 2024, 2025]:
            # 获取年份数据
            if year == 2023:
                year_data = self.df[self.df['is_2023'] == 1].copy()
            elif year == 2024:
                year_data = self.df[self.df['is_2024'] == 1].copy()
            else:
                year_data = self.df[(self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)].copy()
            
            if len(year_data) < 10:  # 样本数太少
                self.logger.warning(f"{year}年样本数太少: {len(year_data)}")
                continue
            
            try:
                # 计算相关系数
                correlation = year_data['price_ratio'].corr(year_data['logit_night_ratio'])
                
                # 拟合回归模型
                X = year_data[base_vars + ['price_ratio']].dropna()
                y = year_data.loc[X.index, 'logit_night_ratio']
                X_with_const = sm.add_constant(X)
                
                model = sm.OLS(y, X_with_const).fit()
                
                price_coef = model.params.get('price_ratio', None)
                price_pvalue = model.pvalues.get('price_ratio', None)
                
                # 计算价格比的基本统计
                price_stats = year_data['price_ratio'].describe()
                logit_stats = year_data['logit_night_ratio'].describe()
                
                year_results[year] = {
                    'sample_size': len(X),
                    'r_squared': model.rsquared,
                    'adj_r_squared': model.rsquared_adj,
                    'price_ratio_coef': price_coef,
                    'price_ratio_pvalue': price_pvalue,
                    'correlation': correlation,
                    'price_ratio_mean': price_stats['mean'],
                    'price_ratio_std': price_stats['std'],
                    'logit_night_ratio_mean': logit_stats['mean'],
                    'logit_night_ratio_std': logit_stats['std']
                }
                
                significance = "***" if price_pvalue < 0.001 else "**" if price_pvalue < 0.01 else "*" if price_pvalue < 0.05 else ""
                self.logger.info(f"{year}年 - 样本数: {len(X)}, 价格比系数: {price_coef:.6f} {significance}, 相关系数: {correlation:.4f}")
                
            except Exception as e:
                self.logger.error(f"{year}年模型拟合失败: {e}")
        
        return year_results
    
    def create_comprehensive_visualization(self, model_results, year_results):
        """创建综合可视化分析"""
        fig = plt.figure(figsize=(16, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. 模型比较 - 价格比系数
        ax1 = fig.add_subplot(gs[0, 0])
        model_names = []
        price_coeffs = []
        
        for name, result in model_results.items():
            if result and result['price_ratio_coef'] is not None:
                model_names.append(name.replace('logit', ''))
                price_coeffs.append(result['price_ratio_coef'])
        
        bars1 = ax1.bar(range(len(model_names)), price_coeffs, alpha=0.7, color=['skyblue', 'orange'])
        ax1.set_xticks(range(len(model_names)))
        ax1.set_xticklabels(model_names, rotation=0)
        ax1.set_ylabel('价格比系数')
        ax1.set_title('模型价格比系数比较')
        ax1.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, coef) in enumerate(zip(bars1, price_coeffs)):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{coef:.4f}', ha='center', va='bottom', fontsize=10)
        
        # 2. 模型比较 - R²
        ax2 = fig.add_subplot(gs[0, 1])
        r_squared_values = [result['r_squared'] for result in model_results.values() if result]
        
        bars2 = ax2.bar(range(len(model_names)), r_squared_values, alpha=0.7, color=['lightgreen', 'lightcoral'])
        ax2.set_xticks(range(len(model_names)))
        ax2.set_xticklabels(model_names, rotation=0)
        ax2.set_ylabel('R²')
        ax2.set_title('模型R²比较')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, r2) in enumerate(zip(bars2, r_squared_values)):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                    f'{r2:.4f}', ha='center', va='bottom', fontsize=10)
        
        # 3. 模型比较 - AIC
        ax3 = fig.add_subplot(gs[0, 2])
        aic_values = [result['aic'] for result in model_results.values() if result]
        
        bars3 = ax3.bar(range(len(model_names)), aic_values, alpha=0.7, color=['gold', 'purple'])
        ax3.set_xticks(range(len(model_names)))
        ax3.set_xticklabels(model_names, rotation=0)
        ax3.set_ylabel('AIC')
        ax3.set_title('模型AIC比较 (越小越好)')
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, aic) in enumerate(zip(bars3, aic_values)):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                    f'{aic:.1f}', ha='center', va='bottom', fontsize=10)
        
        # 4. 年份分析 - 价格比系数
        ax4 = fig.add_subplot(gs[1, 0])
        years = list(year_results.keys())
        year_coeffs = [year_results[year]['price_ratio_coef'] for year in years]
        
        colors = ['red' if coef < 0 else 'green' for coef in year_coeffs]
        bars4 = ax4.bar(years, year_coeffs, alpha=0.7, color=colors)
        ax4.set_xlabel('年份')
        ax4.set_ylabel('价格比系数')
        ax4.set_title('各年份价格比系数')
        ax4.axhline(y=0, color='black', linestyle='--', alpha=0.7)
        ax4.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, coef in zip(bars4, year_coeffs):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + (0.01 if coef >= 0 else -0.03), 
                    f'{coef:.4f}', ha='center', va='bottom' if coef >= 0 else 'top', fontsize=10)
        
        # 5. 年份分析 - 相关系数
        ax5 = fig.add_subplot(gs[1, 1])
        year_corrs = [year_results[year]['correlation'] for year in years]
        
        colors = ['red' if corr < 0 else 'green' for corr in year_corrs]
        bars5 = ax5.bar(years, year_corrs, alpha=0.7, color=colors)
        ax5.set_xlabel('年份')
        ax5.set_ylabel('相关系数')
        ax5.set_title('各年份价格比-logit夜场占比相关系数')
        ax5.axhline(y=0, color='black', linestyle='--', alpha=0.7)
        ax5.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, corr in zip(bars5, year_corrs):
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + (0.01 if corr >= 0 else -0.03), 
                    f'{corr:.4f}', ha='center', va='bottom' if corr >= 0 else 'top', fontsize=10)
        
        # 6. 年份分析 - R²
        ax6 = fig.add_subplot(gs[1, 2])
        year_r2 = [year_results[year]['r_squared'] for year in years]
        
        bars6 = ax6.bar(years, year_r2, alpha=0.7, color='lightblue')
        ax6.set_xlabel('年份')
        ax6.set_ylabel('R²')
        ax6.set_title('各年份模型R²')
        ax6.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, r2 in zip(bars6, year_r2):
            ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{r2:.4f}', ha='center', va='bottom', fontsize=10)
        
        # 7. 散点图 - 价格比 vs logit夜场占比 (按年份着色)
        ax7 = fig.add_subplot(gs[2, :])
        
        colors_map = {2023: 'red', 2024: 'blue', 2025: 'green'}
        
        for year in [2023, 2024, 2025]:
            if year == 2023:
                year_data = self.df[self.df['is_2023'] == 1]
            elif year == 2024:
                year_data = self.df[self.df['is_2024'] == 1]
            else:
                year_data = self.df[(self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)]
            
            if len(year_data) > 0:
                ax7.scatter(year_data['price_ratio'], year_data['logit_night_ratio'], 
                           alpha=0.6, s=30, color=colors_map[year], label=f'{year}年')
                
                # 添加趋势线
                if len(year_data) > 5:
                    z = np.polyfit(year_data['price_ratio'], year_data['logit_night_ratio'], 1)
                    p = np.poly1d(z)
                    x_trend = np.linspace(year_data['price_ratio'].min(), year_data['price_ratio'].max(), 100)
                    ax7.plot(x_trend, p(x_trend), color=colors_map[year], linestyle='--', alpha=0.8)
        
        ax7.set_xlabel('价格比')
        ax7.set_ylabel('logit夜场人次占比')
        ax7.set_title('价格比 vs logit夜场人次占比 (按年份分组)')
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        
        plt.suptitle('logit夜场人次占比模型改进分析', fontsize=16, fontweight='bold')
        plt.savefig('output/logit_model_improvement_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_comparison_table(self, model_results, year_results):
        """生成模型比较表格"""
        print("\n" + "="*100)
        print("logit夜场人次占比模型改进分析报告")
        print("="*100)
        
        # 1. 模型整体比较
        print("\n1. 模型整体性能比较:")
        print("-" * 80)
        print(f"{'指标':<20} {'原始模型':<15} {'交互项模型':<15} {'改进幅度':<15}")
        print("-" * 80)
        
        original = model_results.get('原始logit模型')
        improved = model_results.get('交互项logit模型')
        
        if original and improved:
            r2_improvement = improved['r_squared'] - original['r_squared']
            aic_improvement = original['aic'] - improved['aic']  # AIC越小越好
            
            print(f"{'R²':<20} {original['r_squared']:<15.4f} {improved['r_squared']:<15.4f} {r2_improvement:+.4f}")
            print(f"{'调整R²':<20} {original['adj_r_squared']:<15.4f} {improved['adj_r_squared']:<15.4f} {improved['adj_r_squared']-original['adj_r_squared']:+.4f}")
            print(f"{'AIC':<20} {original['aic']:<15.2f} {improved['aic']:<15.2f} {aic_improvement:+.2f}")
            print(f"{'BIC':<20} {original['bic']:<15.2f} {improved['bic']:<15.2f} {original['bic']-improved['bic']:+.2f}")
        
        # 2. 价格比系数比较
        print("\n2. 价格比系数比较:")
        print("-" * 80)
        print(f"{'模型':<20} {'价格比系数':<15} {'p值':<15} {'显著性':<10}")
        print("-" * 80)
        
        for name, result in model_results.items():
            if result and result['price_ratio_coef'] is not None:
                coef = result['price_ratio_coef']
                pval = result['price_ratio_pvalue']
                significance = "***" if pval < 0.001 else "**" if pval < 0.01 else "*" if pval < 0.05 else ""
                print(f"{name:<20} {coef:<15.6f} {pval:<15.4f} {significance:<10}")
        
        # 3. 交互项系数（如果存在）
        if improved and improved['interaction_2023_coef'] is not None:
            print("\n3. 交互项系数:")
            print("-" * 80)
            print(f"{'交互项':<25} {'系数':<15} {'p值':<15} {'显著性':<10}")
            print("-" * 80)
            
            coef_2023 = improved['interaction_2023_coef']
            pval_2023 = improved['interaction_2023_pvalue']
            sig_2023 = "***" if pval_2023 < 0.001 else "**" if pval_2023 < 0.01 else "*" if pval_2023 < 0.05 else ""
            
            coef_2024 = improved['interaction_2024_coef']
            pval_2024 = improved['interaction_2024_pvalue']
            sig_2024 = "***" if pval_2024 < 0.001 else "**" if pval_2024 < 0.01 else "*" if pval_2024 < 0.05 else ""
            
            print(f"{'price_ratio_x_2023':<25} {coef_2023:<15.6f} {pval_2023:<15.4f} {sig_2023:<10}")
            print(f"{'price_ratio_x_2024':<25} {coef_2024:<15.6f} {pval_2024:<15.4f} {sig_2024:<10}")
        
        # 4. 年份分析
        print("\n4. 各年份价格比效应分析:")
        print("-" * 100)
        print(f"{'年份':<8} {'样本数':<8} {'价格比系数':<15} {'p值':<10} {'显著性':<10} {'相关系数':<12} {'R²':<10}")
        print("-" * 100)
        
        for year, result in year_results.items():
            coef = result['price_ratio_coef']
            pval = result['price_ratio_pvalue']
            corr = result['correlation']
            r2 = result['r_squared']
            sample_size = result['sample_size']
            significance = "***" if pval < 0.001 else "**" if pval < 0.01 else "*" if pval < 0.05 else ""
            
            print(f"{year:<8} {sample_size:<8} {coef:<15.6f} {pval:<10.4f} {significance:<10} {corr:<12.4f} {r2:<10.4f}")
        
        return True

    def run_comprehensive_analysis(self):
        """运行综合改进分析"""
        self.logger.info("开始运行logit夜场人次占比模型综合改进分析")

        if not self.load_and_prepare_data():
            return None

        # 比较原始模型与改进模型
        model_results = self.compare_models()

        # 按年份分析
        year_results = self.analyze_by_year()

        # 生成比较表格
        self.generate_comparison_table(model_results, year_results)

        # 创建可视化
        self.create_comprehensive_visualization(model_results, year_results)

        # 生成最终建议
        self._generate_recommendations(model_results, year_results)

        return {
            'model_results': model_results,
            'year_results': year_results
        }

    def _generate_recommendations(self, model_results, year_results):
        """生成最终建议"""
        print("\n" + "="*100)
        print("最终建议和结论")
        print("="*100)

        original = model_results.get('原始logit模型')
        improved = model_results.get('交互项logit模型')

        if original and improved:
            # 模型改进效果评估
            r2_improvement = improved['r_squared'] - original['r_squared']
            aic_improvement = original['aic'] - improved['aic']

            print("\n1. 模型改进效果:")
            print(f"   ✓ R²提升: {r2_improvement:+.4f} ({r2_improvement/original['r_squared']*100:+.2f}%)")
            print(f"   ✓ AIC改善: {aic_improvement:+.2f} (越大越好)")

            # 价格比系数变化
            original_coef = original['price_ratio_coef']
            improved_coef = improved['price_ratio_coef']
            coef_change = improved_coef - original_coef

            print(f"\n2. 价格比系数变化:")
            print(f"   原始模型: {original_coef:.6f}")
            print(f"   改进模型: {improved_coef:.6f}")
            print(f"   变化幅度: {coef_change:+.6f}")

            # 年份效应分析
            print(f"\n3. 年份效应分析:")
            positive_years = []
            negative_years = []

            for year, result in year_results.items():
                if result['price_ratio_coef'] > 0:
                    positive_years.append(year)
                else:
                    negative_years.append(year)

            if positive_years:
                print(f"   正系数年份: {positive_years}")
            if negative_years:
                print(f"   负系数年份: {negative_years}")

            # 模型选择建议
            print(f"\n4. 模型选择建议:")

            if r2_improvement > 0 and aic_improvement > 0:
                print("   ✅ 推荐使用交互项模型")
                print("   理由: R²和AIC都有改善，模型拟合度更好")
            elif r2_improvement > 0:
                print("   ✅ 推荐使用交互项模型")
                print("   理由: R²有改善，模型解释力更强")
            else:
                print("   ⚠️  需要进一步评估")
                print("   理由: 改进效果不明显，需要考虑其他因素")

            # 实际应用建议
            print(f"\n5. 实际应用建议:")

            # 检查是否存在年份异常
            year_coeffs = [result['price_ratio_coef'] for result in year_results.values()]
            if max(year_coeffs) * min(year_coeffs) < 0:  # 存在正负系数
                print("   ⚠️  不同年份价格弹性存在显著差异")
                print("   建议: 在预测时考虑年份特定的价格效应")

            # 检查交互项显著性
            if improved['interaction_2023_pvalue'] < 0.05 or improved['interaction_2024_pvalue'] < 0.05:
                print("   ✓ 年份交互项显著，支持使用交互项模型")
            else:
                print("   ⚠️  年份交互项不显著，可能存在过度拟合风险")


if __name__ == "__main__":
    # 运行logit模型改进分析
    analyzer = LogitModelImprovement()
    results = analyzer.run_comprehensive_analysis()

    if results:
        print("\n" + "="*80)
        print("✓ logit夜场人次占比模型改进分析完成")
        print("详细结果已保存到 output/logit_model_improvement_analysis.png")
        print("="*80)
    else:
        print("✗ logit模型改进分析失败")
