"""
改进的回归模型分析
解决平均客单价系数为正的问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import statsmodels.api as sm
from data_processor import DataProcessor
from utils import ChengduAnalysisLogger

class ImprovedModelAnalysis:
    """改进的模型分析类"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = ChengduAnalysisLogger("ImprovedModelAnalysis")
        self.processor = DataProcessor()
        self.df = None
        
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        self.df = self.processor.get_processed_data()
        if self.df is None:
            self.logger.error("无法加载数据")
            return False
        
        # 创建改进的变量
        self._create_improved_variables()
        return True
    
    def _create_improved_variables(self):
        """创建改进的变量"""
        # 1. 年份与平均客单价的交互项
        self.df['spending_x_2023'] = self.df['avg_customer_spending'] * self.df['is_2023']
        self.df['spending_x_2024'] = self.df['avg_customer_spending'] * self.df['is_2024']
        
        # 2. 去趋势的平均客单价（按年份去中心化）
        self.df['spending_detrended'] = 0.0
        
        for year in [2023, 2024, 2025]:
            if year == 2023:
                year_mask = self.df['is_2023'] == 1
            elif year == 2024:
                year_mask = self.df['is_2024'] == 1
            else:
                year_mask = (self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)
            
            if year_mask.sum() > 0:
                year_mean = self.df.loc[year_mask, 'avg_customer_spending'].mean()
                self.df.loc[year_mask, 'spending_detrended'] = (
                    self.df.loc[year_mask, 'avg_customer_spending'] - year_mean
                )
                self.logger.info(f"{year}年平均客单价均值: {year_mean:.2f}")
        
        # 3. 相对价格（相对于年度均值的比例）
        year_means = self.df.groupby(['is_2023', 'is_2024'])['avg_customer_spending'].transform('mean')
        self.df['relative_spending'] = self.df['avg_customer_spending'] / year_means
        
        # 4. 对数变换
        self.df['log_avg_customer_spending'] = np.log(self.df['avg_customer_spending'])
        
        self.logger.info("改进变量创建完成")
    
    def compare_models(self):
        """比较不同模型的效果"""
        self.logger.info("开始比较不同模型效果")
        
        # 基础变量
        base_vars = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                    'is_2023', 'is_2024', 'temperature_c', 'temperature_c_sq',
                    'precipitation', 'holiday_day_idx_c', 'holiday_day_idx_c_sq']
        
        models = {
            '原始模型': base_vars + ['avg_customer_spending'],
            '交互项模型': base_vars + ['avg_customer_spending', 'spending_x_2023', 'spending_x_2024'],
            '去趋势模型': base_vars + ['spending_detrended'],
            '相对价格模型': base_vars + ['relative_spending'],
            '对数模型': base_vars + ['log_avg_customer_spending']
        }
        
        results = {}
        
        for model_name, variables in models.items():
            try:
                # 准备数据
                X = self.df[variables].dropna()
                y = self.df.loc[X.index, 'ln_total_visitors']
                X_with_const = sm.add_constant(X)
                
                # 拟合模型
                model = sm.OLS(y, X_with_const).fit()
                
                # 提取价格相关系数
                price_coef = None
                price_pvalue = None
                
                if 'avg_customer_spending' in variables:
                    price_coef = model.params.get('avg_customer_spending', None)
                    price_pvalue = model.pvalues.get('avg_customer_spending', None)
                elif 'spending_detrended' in variables:
                    price_coef = model.params.get('spending_detrended', None)
                    price_pvalue = model.pvalues.get('spending_detrended', None)
                elif 'relative_spending' in variables:
                    price_coef = model.params.get('relative_spending', None)
                    price_pvalue = model.pvalues.get('relative_spending', None)
                elif 'log_avg_customer_spending' in variables:
                    price_coef = model.params.get('log_avg_customer_spending', None)
                    price_pvalue = model.pvalues.get('log_avg_customer_spending', None)
                
                results[model_name] = {
                    'r_squared': model.rsquared,
                    'adj_r_squared': model.rsquared_adj,
                    'aic': model.aic,
                    'price_coefficient': price_coef,
                    'price_pvalue': price_pvalue,
                    'model_object': model
                }
                
                self.logger.info(f"{model_name} - R²: {model.rsquared:.4f}, 价格系数: {price_coef:.6f}")
                
            except Exception as e:
                self.logger.error(f"模型 {model_name} 拟合失败: {e}")
                results[model_name] = None
        
        return results
    
    def analyze_by_year(self):
        """按年份分别分析"""
        self.logger.info("开始按年份分别分析")
        
        base_vars = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                    'temperature_c', 'temperature_c_sq', 'precipitation', 
                    'holiday_day_idx_c', 'holiday_day_idx_c_sq']
        
        year_results = {}
        
        for year in [2023, 2024, 2025]:
            # 获取年份数据
            if year == 2023:
                year_data = self.df[self.df['is_2023'] == 1].copy()
            elif year == 2024:
                year_data = self.df[self.df['is_2024'] == 1].copy()
            else:
                year_data = self.df[(self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)].copy()
            
            if len(year_data) < 10:  # 样本数太少
                continue
            
            try:
                # 拟合模型
                X = year_data[base_vars + ['avg_customer_spending']].dropna()
                y = year_data.loc[X.index, 'ln_total_visitors']
                X_with_const = sm.add_constant(X)
                
                model = sm.OLS(y, X_with_const).fit()
                
                price_coef = model.params.get('avg_customer_spending', None)
                price_pvalue = model.pvalues.get('avg_customer_spending', None)
                
                year_results[year] = {
                    'sample_size': len(X),
                    'r_squared': model.rsquared,
                    'price_coefficient': price_coef,
                    'price_pvalue': price_pvalue,
                    'correlation': year_data['avg_customer_spending'].corr(year_data['ln_total_visitors'])
                }
                
                self.logger.info(f"{year}年 - 样本数: {len(X)}, 价格系数: {price_coef:.6f}, p值: {price_pvalue:.4f}")
                
            except Exception as e:
                self.logger.error(f"{year}年模型拟合失败: {e}")
        
        return year_results
    
    def create_visualization(self, model_results, year_results):
        """创建可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 模型比较 - 价格系数
        model_names = []
        price_coeffs = []
        
        for name, result in model_results.items():
            if result and result['price_coefficient'] is not None:
                model_names.append(name)
                price_coeffs.append(result['price_coefficient'])
        
        axes[0, 0].bar(range(len(model_names)), price_coeffs, alpha=0.7)
        axes[0, 0].set_xticks(range(len(model_names)))
        axes[0, 0].set_xticklabels(model_names, rotation=45)
        axes[0, 0].set_ylabel('价格系数')
        axes[0, 0].set_title('不同模型的价格系数比较')
        axes[0, 0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 模型比较 - R²
        r_squared_values = [result['r_squared'] for result in model_results.values() if result]
        valid_model_names = [name for name, result in model_results.items() if result]
        
        axes[0, 1].bar(range(len(valid_model_names)), r_squared_values, alpha=0.7, color='orange')
        axes[0, 1].set_xticks(range(len(valid_model_names)))
        axes[0, 1].set_xticklabels(valid_model_names, rotation=45)
        axes[0, 1].set_ylabel('R²')
        axes[0, 1].set_title('不同模型的R²比较')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 年份分析 - 价格系数
        years = list(year_results.keys())
        year_coeffs = [year_results[year]['price_coefficient'] for year in years]
        
        axes[1, 0].bar(years, year_coeffs, alpha=0.7, color='green')
        axes[1, 0].set_xlabel('年份')
        axes[1, 0].set_ylabel('价格系数')
        axes[1, 0].set_title('各年份价格系数')
        axes[1, 0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 年份分析 - 相关系数
        year_corrs = [year_results[year]['correlation'] for year in years]
        
        axes[1, 1].bar(years, year_corrs, alpha=0.7, color='purple')
        axes[1, 1].set_xlabel('年份')
        axes[1, 1].set_ylabel('相关系数')
        axes[1, 1].set_title('各年份价格-人次相关系数')
        axes[1, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('output/improved_model_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_comprehensive_analysis(self):
        """运行综合改进分析"""
        self.logger.info("开始运行综合改进分析")
        
        if not self.load_and_prepare_data():
            return None
        
        # 比较不同模型
        model_results = self.compare_models()
        
        # 按年份分析
        year_results = self.analyze_by_year()
        
        # 创建可视化
        self.create_visualization(model_results, year_results)
        
        # 输出总结
        self._print_summary(model_results, year_results)
        
        return {
            'model_results': model_results,
            'year_results': year_results
        }
    
    def _print_summary(self, model_results, year_results):
        """打印分析总结"""
        print("\n" + "="*80)
        print("改进模型分析总结")
        print("="*80)
        
        print("\n1. 不同模型价格系数比较:")
        for name, result in model_results.items():
            if result and result['price_coefficient'] is not None:
                coef = result['price_coefficient']
                pval = result['price_pvalue']
                significance = "***" if pval < 0.001 else "**" if pval < 0.01 else "*" if pval < 0.05 else ""
                print(f"   {name}: {coef:.6f} (p={pval:.4f}) {significance}")
        
        print("\n2. 各年份价格系数:")
        for year, result in year_results.items():
            coef = result['price_coefficient']
            pval = result['price_pvalue']
            corr = result['correlation']
            significance = "***" if pval < 0.001 else "**" if pval < 0.01 else "*" if pval < 0.05 else ""
            print(f"   {year}年: 系数={coef:.6f} (p={pval:.4f}) {significance}, 相关系数={corr:.4f}")
        
        print("\n3. 关键发现:")
        # 找出最佳模型
        best_model = None
        best_negative_coef = float('inf')
        
        for name, result in model_results.items():
            if result and result['price_coefficient'] is not None:
                coef = result['price_coefficient']
                if coef < 0 and abs(coef) < best_negative_coef:
                    best_negative_coef = abs(coef)
                    best_model = name
        
        if best_model:
            print(f"   - 最佳改进模型: {best_model}")
            print(f"   - 该模型价格系数为负: {model_results[best_model]['price_coefficient']:.6f}")
        
        # 年份差异
        negative_years = [year for year, result in year_results.items() if result['price_coefficient'] < 0]
        positive_years = [year for year, result in year_results.items() if result['price_coefficient'] > 0]
        
        if negative_years:
            print(f"   - 价格系数为负的年份: {negative_years}")
        if positive_years:
            print(f"   - 价格系数为正的年份: {positive_years}")


if __name__ == "__main__":
    analyzer = ImprovedModelAnalysis()
    results = analyzer.run_comprehensive_analysis()
    
    if results:
        print("\n✓ 改进模型分析完成")
        print("详细结果已保存到output/improved_model_analysis.png")
    else:
        print("✗ 改进模型分析失败")
