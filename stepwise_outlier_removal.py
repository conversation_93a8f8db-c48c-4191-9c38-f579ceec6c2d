"""
逐步剔除异常样本分析
目标：通过系统性剔除异常样本，使logit夜场人次占比模型的价格比系数转为正值
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import statsmodels.api as sm
from itertools import combinations
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from data_processor import DataProcessor
from utils import ChengduAnalysisLogger

class StepwiseOutlierRemovalAnalysis:
    """逐步剔除异常样本分析类"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = ChengduAnalysisLogger("StepwiseOutlierRemoval")
        self.processor = DataProcessor()
        self.df = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 异常样本列表（基于方法5的识别结果）
        self.outlier_samples = [
            '2025-07-03',  # 最异常样本
            '2025-06-28',
            '2025-07-19', 
            '2025-07-05',
            '2025-07-26'
        ]
        
        # 回归变量
        self.base_vars = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                         'is_2023', 'is_2024', 'temperature_c', 'temperature_c_sq',
                         'precipitation', 'holiday_day_idx_c', 'holiday_day_idx_c_sq']
        
        self.logger.info("逐步剔除异常样本分析器初始化完成")
        self.logger.info(f"目标异常样本: {self.outlier_samples}")
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        self.df = self.processor.get_processed_data()
        if self.df is None:
            self.logger.error("无法加载数据")
            return False
        
        # 确保date列是datetime类型
        self.df['date'] = pd.to_datetime(self.df['date'])
        
        # 添加日期字符串列用于匹配
        self.df['date_str'] = self.df['date'].dt.strftime('%Y-%m-%d')
        
        self.logger.info(f"数据加载成功，总样本数: {len(self.df)}")
        
        # 验证异常样本是否存在
        for sample_date in self.outlier_samples:
            if sample_date not in self.df['date_str'].values:
                self.logger.warning(f"异常样本 {sample_date} 在数据中不存在")
        
        return True
    
    def run_regression(self, excluded_samples=None):
        """运行logit夜场人次占比回归分析
        
        Args:
            excluded_samples: 要排除的样本日期列表
            
        Returns:
            dict: 回归结果字典
        """
        try:
            # 准备数据
            data = self.df.copy()
            
            # 排除指定样本
            if excluded_samples:
                data = data[~data['date_str'].isin(excluded_samples)]
            
            # 准备回归变量
            X = data[['price_ratio'] + self.base_vars].dropna()
            y = data.loc[X.index, 'logit_night_ratio']
            X_with_const = sm.add_constant(X)
            
            # 拟合模型
            model = sm.OLS(y, X_with_const).fit()
            
            # 提取结果
            price_coef = model.params.get('price_ratio', np.nan)
            price_pvalue = model.pvalues.get('price_ratio', np.nan)
            r_squared = model.rsquared
            adj_r_squared = model.rsquared_adj
            aic = model.aic
            
            return {
                'model': model,
                'sample_size': len(X),
                'price_coefficient': price_coef,
                'price_pvalue': price_pvalue,
                'r_squared': r_squared,
                'adj_r_squared': adj_r_squared,
                'aic': aic,
                'excluded_samples': excluded_samples or [],
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"回归分析失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'excluded_samples': excluded_samples or []
            }
    
    def single_sample_removal_test(self):
        """单样本剔除测试"""
        self.logger.info("=" * 80)
        self.logger.info("单样本剔除测试")
        self.logger.info("=" * 80)
        
        results = []
        
        # 基准模型（不剔除任何样本）
        baseline_result = self.run_regression()
        results.append({
            'removed_samples': '无',
            'removed_count': 0,
            'sample_size': baseline_result['sample_size'],
            'price_coefficient': baseline_result['price_coefficient'],
            'price_pvalue': baseline_result['price_pvalue'],
            'r_squared': baseline_result['r_squared'],
            'aic': baseline_result['aic'],
            'coefficient_positive': baseline_result['price_coefficient'] > 0,
            'significant': baseline_result['price_pvalue'] < 0.05
        })
        
        print(f"基准模型 - 样本数: {baseline_result['sample_size']}, "
              f"价格比系数: {baseline_result['price_coefficient']:.6f}, "
              f"p值: {baseline_result['price_pvalue']:.4f}")
        
        # 逐一剔除单个样本
        for sample_date in self.outlier_samples:
            result = self.run_regression([sample_date])
            
            if result['success']:
                results.append({
                    'removed_samples': sample_date,
                    'removed_count': 1,
                    'sample_size': result['sample_size'],
                    'price_coefficient': result['price_coefficient'],
                    'price_pvalue': result['price_pvalue'],
                    'r_squared': result['r_squared'],
                    'aic': result['aic'],
                    'coefficient_positive': result['price_coefficient'] > 0,
                    'significant': result['price_pvalue'] < 0.05
                })
                
                status = "✓ 转正" if result['price_coefficient'] > 0 else "✗ 仍负"
                significance = "显著" if result['price_pvalue'] < 0.05 else "不显著"
                
                print(f"剔除 {sample_date} - 样本数: {result['sample_size']}, "
                      f"价格比系数: {result['price_coefficient']:.6f} ({status}), "
                      f"p值: {result['price_pvalue']:.4f} ({significance})")
        
        return results
    
    def double_sample_removal_test(self):
        """双样本剔除测试"""
        self.logger.info("=" * 80)
        self.logger.info("双样本剔除测试")
        self.logger.info("=" * 80)
        
        results = []
        
        # 生成所有可能的两样本组合
        for combo in combinations(self.outlier_samples, 2):
            excluded_samples = list(combo)
            result = self.run_regression(excluded_samples)
            
            if result['success']:
                results.append({
                    'removed_samples': ' + '.join(excluded_samples),
                    'removed_count': 2,
                    'sample_size': result['sample_size'],
                    'price_coefficient': result['price_coefficient'],
                    'price_pvalue': result['price_pvalue'],
                    'r_squared': result['r_squared'],
                    'aic': result['aic'],
                    'coefficient_positive': result['price_coefficient'] > 0,
                    'significant': result['price_pvalue'] < 0.05
                })
                
                status = "✓ 转正" if result['price_coefficient'] > 0 else "✗ 仍负"
                significance = "显著" if result['price_pvalue'] < 0.05 else "不显著"
                
                print(f"剔除 {' + '.join(excluded_samples)} - 样本数: {result['sample_size']}, "
                      f"价格比系数: {result['price_coefficient']:.6f} ({status}), "
                      f"p值: {result['price_pvalue']:.4f} ({significance})")
                
                # 如果找到正系数且显著，记录
                if result['price_coefficient'] > 0 and result['price_pvalue'] < 0.05:
                    self.logger.info(f"🎯 找到最优双样本组合: {' + '.join(excluded_samples)}")
        
        return results
    
    def multi_sample_removal_test(self, max_samples=5):
        """多样本剔除测试"""
        self.logger.info("=" * 80)
        self.logger.info("多样本剔除测试")
        self.logger.info("=" * 80)
        
        all_results = []
        
        # 测试3到5个样本的组合
        for n_samples in range(3, max_samples + 1):
            print(f"\n测试剔除 {n_samples} 个样本的组合:")
            
            results = []
            for combo in combinations(self.outlier_samples, n_samples):
                excluded_samples = list(combo)
                result = self.run_regression(excluded_samples)
                
                if result['success']:
                    result_dict = {
                        'removed_samples': ' + '.join(excluded_samples),
                        'removed_count': n_samples,
                        'sample_size': result['sample_size'],
                        'price_coefficient': result['price_coefficient'],
                        'price_pvalue': result['price_pvalue'],
                        'r_squared': result['r_squared'],
                        'aic': result['aic'],
                        'coefficient_positive': result['price_coefficient'] > 0,
                        'significant': result['price_pvalue'] < 0.05
                    }
                    
                    results.append(result_dict)
                    all_results.append(result_dict)
                    
                    status = "✓ 转正" if result['price_coefficient'] > 0 else "✗ 仍负"
                    significance = "显著" if result['price_pvalue'] < 0.05 else "不显著"
                    
                    print(f"剔除 {' + '.join(excluded_samples)} - 样本数: {result['sample_size']}, "
                          f"价格比系数: {result['price_coefficient']:.6f} ({status}), "
                          f"p值: {result['price_pvalue']:.4f} ({significance})")
                    
                    # 如果找到正系数且显著，记录
                    if result['price_coefficient'] > 0 and result['price_pvalue'] < 0.05:
                        self.logger.info(f"🎯 找到最优{n_samples}样本组合: {' + '.join(excluded_samples)}")
            
            # 如果在当前样本数下找到了正系数且显著的组合，可以考虑停止
            positive_significant = [r for r in results if r['coefficient_positive'] and r['significant']]
            if positive_significant:
                self.logger.info(f"在{n_samples}样本剔除中找到{len(positive_significant)}个有效组合")
        
        return all_results
    
    def create_visualization(self, all_results):
        """创建可视化图表"""
        self.logger.info("创建可视化图表")

        # 准备数据
        results_df = pd.DataFrame(all_results)

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 1. 价格比系数变化图
        x_pos = range(len(results_df))
        colors = ['red' if coef < 0 else 'green' for coef in results_df['price_coefficient']]

        bars1 = axes[0, 0].bar(x_pos, results_df['price_coefficient'], color=colors, alpha=0.7)
        axes[0, 0].axhline(y=0, color='black', linestyle='--', alpha=0.7)
        axes[0, 0].set_xlabel('剔除组合')
        axes[0, 0].set_ylabel('价格比系数')
        axes[0, 0].set_title('价格比系数变化')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. R²变化图
        axes[0, 1].plot(x_pos, results_df['r_squared'], marker='o', linewidth=2, markersize=6)
        axes[0, 1].set_xlabel('剔除组合')
        axes[0, 1].set_ylabel('R²')
        axes[0, 1].set_title('模型拟合度变化')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. p值变化图
        axes[0, 2].plot(x_pos, results_df['price_pvalue'], marker='s', linewidth=2, markersize=6, color='orange')
        axes[0, 2].axhline(y=0.05, color='red', linestyle='--', alpha=0.7, label='显著性阈值')
        axes[0, 2].set_xlabel('剔除组合')
        axes[0, 2].set_ylabel('p值')
        axes[0, 2].set_title('价格比系数显著性变化')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # 4. 样本数变化图
        axes[1, 0].bar(x_pos, results_df['sample_size'], alpha=0.7, color='skyblue')
        axes[1, 0].set_xlabel('剔除组合')
        axes[1, 0].set_ylabel('剩余样本数')
        axes[1, 0].set_title('剩余样本数变化')
        axes[1, 0].grid(True, alpha=0.3)

        # 5. 剔除样本数 vs 价格比系数
        scatter_colors = ['red' if coef < 0 else 'green' for coef in results_df['price_coefficient']]
        axes[1, 1].scatter(results_df['removed_count'], results_df['price_coefficient'],
                          c=scatter_colors, s=100, alpha=0.7)
        axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.7)
        axes[1, 1].set_xlabel('剔除样本数')
        axes[1, 1].set_ylabel('价格比系数')
        axes[1, 1].set_title('剔除样本数 vs 价格比系数')
        axes[1, 1].grid(True, alpha=0.3)

        # 6. 最优组合对比
        optimal_results = [r for r in all_results if r['coefficient_positive'] and r['significant']]
        if optimal_results:
            # 按剔除样本数分组
            min_removal_results = {}
            for result in optimal_results:
                count = result['removed_count']
                if count not in min_removal_results or result['price_coefficient'] > min_removal_results[count]['price_coefficient']:
                    min_removal_results[count] = result

            removal_counts = list(min_removal_results.keys())
            coefficients = [min_removal_results[count]['price_coefficient'] for count in removal_counts]

            bars6 = axes[1, 2].bar(removal_counts, coefficients, alpha=0.7, color='purple')
            axes[1, 2].set_xlabel('剔除样本数')
            axes[1, 2].set_ylabel('最优价格比系数')
            axes[1, 2].set_title('各剔除数量下的最优系数')
            axes[1, 2].grid(True, alpha=0.3)

            # 添加数值标签
            for bar, coef in zip(bars6, coefficients):
                axes[1, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                               f'{coef:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('output/stepwise_outlier_removal_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return results_df

    def analyze_optimal_solution(self, all_results):
        """分析最优解决方案"""
        self.logger.info("=" * 80)
        self.logger.info("分析最优解决方案")
        self.logger.info("=" * 80)

        # 找出所有使系数转正且显著的组合
        optimal_results = [r for r in all_results if r['coefficient_positive'] and r['significant']]

        if not optimal_results:
            print("⚠ 未找到使价格比系数转为正值且显著的剔除组合")
            return None

        # 找出最少剔除样本数的最优组合
        min_removal_count = min(r['removed_count'] for r in optimal_results)
        best_minimal_results = [r for r in optimal_results if r['removed_count'] == min_removal_count]

        # 在最少剔除样本数中选择系数最大的
        best_result = max(best_minimal_results, key=lambda x: x['price_coefficient'])

        print(f"🎯 最优解决方案:")
        print(f"   剔除样本: {best_result['removed_samples']}")
        print(f"   剔除样本数: {best_result['removed_count']}")
        print(f"   剩余样本数: {best_result['sample_size']}")
        print(f"   价格比系数: {best_result['price_coefficient']:.6f} (✓ 正值)")
        print(f"   p值: {best_result['price_pvalue']:.6f} (✓ 显著)")
        print(f"   R²: {best_result['r_squared']:.4f}")
        print(f"   AIC: {best_result['aic']:.2f}")

        # 分析被剔除样本的特征
        removed_dates = best_result['removed_samples'].split(' + ')
        removed_samples_data = self.df[self.df['date_str'].isin(removed_dates)].copy()

        if len(removed_samples_data) > 0:
            print(f"\n被剔除样本特征分析:")
            print("-" * 50)

            # 添加星期几信息
            weekday_map = {0: '星期一', 1: '星期二', 2: '星期三', 3: '星期四', 4: '星期五', 5: '星期六', 6: '星期日'}
            removed_samples_data['weekday_name'] = removed_samples_data['date'].dt.dayofweek.map(weekday_map)

            for _, row in removed_samples_data.iterrows():
                print(f"   {row['date'].strftime('%Y-%m-%d')} ({row['weekday_name']}) - "
                      f"价格比: {row['price_ratio']:.4f}, logit占比: {row['logit_night_ratio']:.4f}")

            # 星期几分布
            weekday_counts = removed_samples_data['weekday_name'].value_counts()
            print(f"\n   星期几分布: {dict(weekday_counts)}")

            # 与整体数据对比
            overall_price_mean = self.df['price_ratio'].mean()
            overall_logit_mean = self.df['logit_night_ratio'].mean()
            removed_price_mean = removed_samples_data['price_ratio'].mean()
            removed_logit_mean = removed_samples_data['logit_night_ratio'].mean()

            print(f"\n   与整体数据对比:")
            print(f"     价格比 - 被剔除样本均值: {removed_price_mean:.4f}, 整体均值: {overall_price_mean:.4f}")
            print(f"     logit占比 - 被剔除样本均值: {removed_logit_mean:.4f}, 整体均值: {overall_logit_mean:.4f}")

        return best_result

    def run_comprehensive_analysis(self):
        """运行综合逐步剔除分析"""
        self.logger.info("开始运行逐步剔除异常样本综合分析")

        if not self.load_and_prepare_data():
            return None

        all_results = []

        # 1. 单样本剔除测试
        single_results = self.single_sample_removal_test()
        all_results.extend(single_results)

        # 2. 双样本剔除测试
        double_results = self.double_sample_removal_test()
        all_results.extend(double_results)

        # 3. 多样本剔除测试
        multi_results = self.multi_sample_removal_test()
        all_results.extend(multi_results)

        # 4. 创建可视化
        results_df = self.create_visualization(all_results)

        # 5. 分析最优解决方案
        best_result = self.analyze_optimal_solution(all_results)

        # 6. 生成详细结果表格
        self._generate_detailed_results_table(all_results)

        return {
            'all_results': all_results,
            'best_result': best_result,
            'results_dataframe': results_df
        }
    
    def _generate_detailed_results_table(self, all_results):
        """生成详细结果表格"""
        print("\n" + "="*120)
        print("详细结果表格")
        print("="*120)
        
        print(f"{'剔除样本':<40} {'剔除数':<8} {'样本数':<8} {'价格比系数':<15} {'p值':<10} {'R²':<10} {'转正':<6} {'显著':<6}")
        print("-"*120)
        
        for result in all_results:
            removed = result['removed_samples']
            if len(removed) > 35:
                removed = removed[:32] + "..."
            
            positive_mark = "✓" if result['coefficient_positive'] else "✗"
            significant_mark = "✓" if result['significant'] else "✗"
            
            print(f"{removed:<40} {result['removed_count']:<8} {result['sample_size']:<8} "
                  f"{result['price_coefficient']:<15.6f} {result['price_pvalue']:<10.4f} "
                  f"{result['r_squared']:<10.4f} {positive_mark:<6} {significant_mark:<6}")


if __name__ == "__main__":
    # 运行逐步剔除异常样本分析
    analyzer = StepwiseOutlierRemovalAnalysis()
    results = analyzer.run_comprehensive_analysis()
    
    if results:
        print("\n" + "="*80)
        print("✓ 逐步剔除异常样本分析完成")
        print("="*80)
    else:
        print("✗ 逐步剔除异常样本分析失败")
