"""
平均客单价系数诊断分析
分析为什么ln总人次模型中平均客单价系数为正的原因
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import statsmodels.api as sm
from sklearn.preprocessing import StandardScaler
from statsmodels.stats.outliers_influence import variance_inflation_factor
import warnings
warnings.filterwarnings('ignore')

# 导入项目模块
from data_processor import DataProcessor
from utils import ChengduAnalysisLogger

class CustomerSpendingDiagnostic:
    """平均客单价诊断分析类"""
    
    def __init__(self):
        """初始化诊断分析器"""
        self.logger = ChengduAnalysisLogger("CustomerSpendingDiagnostic")
        self.processor = DataProcessor()
        self.df = None
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        self.logger.info("平均客单价诊断分析器初始化完成")
    
    def load_data(self):
        """加载数据"""
        self.df = self.processor.get_processed_data()
        if self.df is None:
            self.logger.error("无法加载数据")
            return False
        
        self.logger.info(f"数据加载成功，数据形状: {self.df.shape}")
        return True
    
    def method1_data_exploration(self):
        """方法1：数据探索分析"""
        self.logger.info("=" * 60)
        self.logger.info("方法1：数据探索分析")
        self.logger.info("=" * 60)
        
        # 基本统计信息
        spending_stats = self.df['avg_customer_spending'].describe()
        visitors_stats = self.df['ln_total_visitors'].describe()
        
        print("平均客单价统计信息:")
        print(spending_stats)
        print("\nln总人次统计信息:")
        print(visitors_stats)
        
        # 相关系数
        correlation = self.df['avg_customer_spending'].corr(self.df['ln_total_visitors'])
        print(f"\n总体相关系数: {correlation:.4f}")
        
        # 绘制散点图
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 2, 1)
        plt.scatter(self.df['avg_customer_spending'], self.df['ln_total_visitors'], alpha=0.6, s=30)
        plt.xlabel('平均客单价')
        plt.ylabel('ln总人次')
        plt.title(f'平均客单价 vs ln总人次\n相关系数: {correlation:.4f}')
        plt.grid(True, alpha=0.3)
        
        # 添加趋势线
        z = np.polyfit(self.df['avg_customer_spending'], self.df['ln_total_visitors'], 1)
        p = np.poly1d(z)
        plt.plot(self.df['avg_customer_spending'], p(self.df['avg_customer_spending']), "r--", alpha=0.8)
        
        # 分布图
        plt.subplot(2, 2, 2)
        plt.hist(self.df['avg_customer_spending'], bins=20, alpha=0.7, edgecolor='black')
        plt.xlabel('平均客单价')
        plt.ylabel('频数')
        plt.title('平均客单价分布')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(2, 2, 3)
        plt.hist(self.df['ln_total_visitors'], bins=20, alpha=0.7, edgecolor='black')
        plt.xlabel('ln总人次')
        plt.ylabel('频数')
        plt.title('ln总人次分布')
        plt.grid(True, alpha=0.3)
        
        # 箱线图
        plt.subplot(2, 2, 4)
        data_to_plot = [self.df['avg_customer_spending'], self.df['ln_total_visitors']]
        plt.boxplot(data_to_plot, labels=['平均客单价', 'ln总人次'])
        plt.title('数据分布箱线图')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('output/method1_data_exploration.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return correlation
    
    def method2_identify_outliers(self):
        """方法2：识别异常样本"""
        self.logger.info("=" * 60)
        self.logger.info("方法2：识别异常样本")
        self.logger.info("=" * 60)
        
        # 计算分位数
        q25_spending = self.df['avg_customer_spending'].quantile(0.25)
        q75_spending = self.df['avg_customer_spending'].quantile(0.75)
        q25_visitors = self.df['ln_total_visitors'].quantile(0.25)
        q75_visitors = self.df['ln_total_visitors'].quantile(0.75)
        
        print(f"平均客单价 Q25: {q25_spending:.2f}, Q75: {q75_spending:.2f}")
        print(f"ln总人次 Q25: {q25_visitors:.2f}, Q75: {q75_visitors:.2f}")
        
        # 找出四个象限的样本
        high_spending_high_visitors = self.df[
            (self.df['avg_customer_spending'] > q75_spending) & 
            (self.df['ln_total_visitors'] > q75_visitors)
        ]
        
        low_spending_low_visitors = self.df[
            (self.df['avg_customer_spending'] < q25_spending) & 
            (self.df['ln_total_visitors'] < q25_visitors)
        ]
        
        high_spending_low_visitors = self.df[
            (self.df['avg_customer_spending'] > q75_spending) & 
            (self.df['ln_total_visitors'] < q25_visitors)
        ]
        
        low_spending_high_visitors = self.df[
            (self.df['avg_customer_spending'] < q25_spending) & 
            (self.df['ln_total_visitors'] > q75_visitors)
        ]
        
        print(f"\n高客单价+高人次样本数: {len(high_spending_high_visitors)}")
        print(f"低客单价+低人次样本数: {len(low_spending_low_visitors)}")
        print(f"高客单价+低人次样本数: {len(high_spending_low_visitors)}")
        print(f"低客单价+高人次样本数: {len(low_spending_high_visitors)}")
        
        # 使用IQR方法识别异常值
        Q1_spending = self.df['avg_customer_spending'].quantile(0.25)
        Q3_spending = self.df['avg_customer_spending'].quantile(0.75)
        IQR_spending = Q3_spending - Q1_spending
        lower_bound = Q1_spending - 1.5 * IQR_spending
        upper_bound = Q3_spending + 1.5 * IQR_spending
        
        outliers = self.df[
            (self.df['avg_customer_spending'] < lower_bound) | 
            (self.df['avg_customer_spending'] > upper_bound)
        ]
        
        print(f"\n平均客单价异常值样本数: {len(outliers)}")
        if len(outliers) > 0:
            print("异常值样本:")
            print(outliers[['date', 'avg_customer_spending', 'ln_total_visitors']].head(10))
        
        # 可视化四象限分析
        plt.figure(figsize=(10, 8))
        plt.scatter(self.df['avg_customer_spending'], self.df['ln_total_visitors'], 
                   alpha=0.6, s=30, label='所有样本')
        
        # 标记四象限
        plt.axhline(y=q75_visitors, color='red', linestyle='--', alpha=0.7)
        plt.axhline(y=q25_visitors, color='red', linestyle='--', alpha=0.7)
        plt.axvline(x=q75_spending, color='red', linestyle='--', alpha=0.7)
        plt.axvline(x=q25_spending, color='red', linestyle='--', alpha=0.7)
        
        # 高亮异常象限
        if len(high_spending_high_visitors) > 0:
            plt.scatter(high_spending_high_visitors['avg_customer_spending'], 
                       high_spending_high_visitors['ln_total_visitors'], 
                       color='red', s=50, label=f'高客单+高人次 ({len(high_spending_high_visitors)})')
        
        if len(low_spending_low_visitors) > 0:
            plt.scatter(low_spending_low_visitors['avg_customer_spending'], 
                       low_spending_low_visitors['ln_total_visitors'], 
                       color='blue', s=50, label=f'低客单+低人次 ({len(low_spending_low_visitors)})')
        
        plt.xlabel('平均客单价')
        plt.ylabel('ln总人次')
        plt.title('四象限分析 - 识别异常样本')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('output/method2_outlier_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return {
            'high_high': len(high_spending_high_visitors),
            'low_low': len(low_spending_low_visitors),
            'high_low': len(high_spending_low_visitors),
            'low_high': len(low_spending_high_visitors),
            'outliers': len(outliers)
        }
    
    def method3_group_analysis(self):
        """方法3：分组分析"""
        self.logger.info("=" * 60)
        self.logger.info("方法3：分组分析")
        self.logger.info("=" * 60)
        
        results = {}
        
        # 按年份分组分析
        print("按年份分组的相关系数:")
        for year in [2023, 2024, 2025]:
            if year == 2023:
                year_data = self.df[self.df['is_2023'] == 1]
            elif year == 2024:
                year_data = self.df[self.df['is_2024'] == 1]
            else:  # 2025
                year_data = self.df[(self.df['is_2023'] == 0) & (self.df['is_2024'] == 0)]
            
            if len(year_data) > 0:
                corr = year_data['avg_customer_spending'].corr(year_data['ln_total_visitors'])
                results[f'year_{year}'] = {'correlation': corr, 'count': len(year_data)}
                print(f"  {year}年: {corr:.4f} (样本数: {len(year_data)})")
        
        # 按星期分组分析
        print("\n按星期分组的相关系数:")
        weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
        weekday_names = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
        
        for day, day_name in zip(weekdays, weekday_names):
            day_data = self.df[self.df[day] == 1]
            if len(day_data) > 0:
                corr = day_data['avg_customer_spending'].corr(day_data['ln_total_visitors'])
                results[f'weekday_{day}'] = {'correlation': corr, 'count': len(day_data)}
                print(f"  {day_name}: {corr:.4f} (样本数: {len(day_data)})")
        
        # 星期日数据
        sunday_data = self.df[self.df[weekdays].sum(axis=1) == 0]
        if len(sunday_data) > 0:
            corr = sunday_data['avg_customer_spending'].corr(sunday_data['ln_total_visitors'])
            results['weekday_sunday'] = {'correlation': corr, 'count': len(sunday_data)}
            print(f"  星期日: {corr:.4f} (样本数: {len(sunday_data)})")
        
        return results

    def method4_multicollinearity_check(self):
        """方法4：检查多重共线性"""
        self.logger.info("=" * 60)
        self.logger.info("方法4：检查多重共线性")
        self.logger.info("=" * 60)

        # 准备自变量
        X_vars = ['avg_customer_spending', 'monday', 'tuesday', 'wednesday', 'thursday',
                  'friday', 'saturday', 'is_2023', 'is_2024', 'temperature_c',
                  'temperature_c_sq', 'precipitation', 'holiday_day_idx_c', 'holiday_day_idx_c_sq']

        X = self.df[X_vars].dropna()

        # 计算相关矩阵
        corr_matrix = X.corr()

        # 找出与平均客单价高度相关的变量
        spending_corr = corr_matrix['avg_customer_spending'].abs().sort_values(ascending=False)
        print("与平均客单价的相关系数:")
        for var, corr in spending_corr.items():
            if var != 'avg_customer_spending':
                print(f"  {var}: {corr:.4f}")

        # 计算VIF
        try:
            vif_data = pd.DataFrame()
            vif_data["变量"] = X.columns
            vif_data["VIF"] = [variance_inflation_factor(X.values, i) for i in range(len(X.columns))]
            vif_data = vif_data.sort_values('VIF', ascending=False)

            print(f"\n方差膨胀因子(VIF):")
            print(vif_data)

            # 标记高VIF变量
            high_vif = vif_data[vif_data['VIF'] > 5]
            if len(high_vif) > 0:
                print(f"\n高VIF变量 (>5):")
                print(high_vif)
        except Exception as e:
            print(f"VIF计算失败: {e}")
            vif_data = None

        # 可视化相关矩阵
        plt.figure(figsize=(12, 10))
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                    square=True, fmt='.3f', cbar_kws={"shrink": .8})
        plt.title('变量相关矩阵')
        plt.tight_layout()
        plt.savefig('output/method4_correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

        return vif_data, spending_corr

    def method5_influence_analysis(self):
        """方法5：残差和影响力分析"""
        self.logger.info("=" * 60)
        self.logger.info("方法5：残差和影响力分析")
        self.logger.info("=" * 60)

        # 准备数据
        X_vars = ['avg_customer_spending', 'monday', 'tuesday', 'wednesday', 'thursday',
                  'friday', 'saturday', 'is_2023', 'is_2024', 'temperature_c',
                  'temperature_c_sq', 'precipitation', 'holiday_day_idx_c', 'holiday_day_idx_c_sq']

        X = self.df[X_vars].dropna()
        y = self.df.loc[X.index, 'ln_total_visitors']
        X_with_const = sm.add_constant(X)

        # 拟合模型
        model = sm.OLS(y, X_with_const).fit()

        # 计算影响力统计量
        influence = model.get_influence()
        leverage = influence.hat_matrix_diag  # 杠杆值
        cooks_d = influence.cooks_distance[0]  # Cook距离
        standardized_residuals = influence.resid_studentized_internal  # 标准化残差

        # 识别高影响力样本
        cook_threshold = 4 / len(X)
        leverage_threshold = 2 * len(X.columns) / len(X)

        high_cook = np.where(cooks_d > cook_threshold)[0]
        high_leverage = np.where(leverage > leverage_threshold)[0]
        high_residual = np.where(np.abs(standardized_residuals) > 2)[0]

        print(f"Cook距离阈值: {cook_threshold:.4f}")
        print(f"杠杆值阈值: {leverage_threshold:.4f}")
        print(f"高Cook距离样本数: {len(high_cook)}")
        print(f"高杠杆值样本数: {len(high_leverage)}")
        print(f"高残差样本数: {len(high_residual)}")

        # 查看高影响力样本
        if len(high_cook) > 0:
            print(f"\n高Cook距离样本 (前10个):")
            high_cook_samples = self.df.iloc[X.index[high_cook[:10]]]
            print(high_cook_samples[['date', 'avg_customer_spending', 'ln_total_visitors']])

        # 可视化影响力分析
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # Cook距离图
        axes[0, 0].stem(range(len(cooks_d)), cooks_d, markerfmt=',')
        axes[0, 0].axhline(y=cook_threshold, color='red', linestyle='--', label=f'阈值 {cook_threshold:.4f}')
        axes[0, 0].set_xlabel('样本索引')
        axes[0, 0].set_ylabel('Cook距离')
        axes[0, 0].set_title('Cook距离图')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 杠杆值图
        axes[0, 1].scatter(range(len(leverage)), leverage, alpha=0.6)
        axes[0, 1].axhline(y=leverage_threshold, color='red', linestyle='--', label=f'阈值 {leverage_threshold:.4f}')
        axes[0, 1].set_xlabel('样本索引')
        axes[0, 1].set_ylabel('杠杆值')
        axes[0, 1].set_title('杠杆值图')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 残差vs拟合值
        fitted_values = model.fittedvalues
        residuals = model.resid
        axes[1, 0].scatter(fitted_values, residuals, alpha=0.6)
        axes[1, 0].axhline(y=0, color='red', linestyle='--')
        axes[1, 0].set_xlabel('拟合值')
        axes[1, 0].set_ylabel('残差')
        axes[1, 0].set_title('残差 vs 拟合值')
        axes[1, 0].grid(True, alpha=0.3)

        # Q-Q图
        stats.probplot(residuals, dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title('残差Q-Q图')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('output/method5_influence_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return {
            'high_cook_indices': high_cook,
            'high_leverage_indices': high_leverage,
            'high_residual_indices': high_residual,
            'cook_distances': cooks_d,
            'leverage_values': leverage
        }

    def run_comprehensive_analysis(self):
        """运行综合诊断分析"""
        self.logger.info("开始运行平均客单价系数综合诊断分析")

        if not self.load_data():
            return None

        results = {}

        # 方法1：数据探索
        results['correlation'] = self.method1_data_exploration()

        # 方法2：异常值识别
        results['outlier_analysis'] = self.method2_identify_outliers()

        # 方法3：分组分析
        results['group_analysis'] = self.method3_group_analysis()

        # 方法4：多重共线性检查
        vif_data, spending_corr = self.method4_multicollinearity_check()
        results['vif_analysis'] = vif_data
        results['spending_correlations'] = spending_corr

        # 方法5：影响力分析
        results['influence_analysis'] = self.method5_influence_analysis()

        return results


if __name__ == "__main__":
    # 运行综合诊断分析
    diagnostic = CustomerSpendingDiagnostic()
    results = diagnostic.run_comprehensive_analysis()

    if results:
        print("\n" + "="*80)
        print("综合诊断分析完成")
        print("="*80)
        print(f"总体相关系数: {results['correlation']:.4f}")
        print(f"异常值样本数: {results['outlier_analysis']['outliers']}")
        print("详细结果已保存到output目录中的图表文件")
    else:
        print("诊断分析失败")
