"""
成都项目回归分析模块
实现两个线性回归模型：ln总人次模型和logit夜场人次占比模型
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error
import statsmodels.api as sm
from scipy import stats
import warnings

from config import MODEL_CONFIG, OUTPUT_CONFIG
from utils import ChengduAnalysisLogger, format_regression_results, get_current_timestamp


class RegressionModel:
    """单个回归模型类"""
    
    def __init__(self, model_name: str, model_config: Dict[str, Any], 
                 logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化回归模型
        
        Args:
            model_name: 模型名称
            model_config: 模型配置字典
            logger: 日志记录器实例
        """
        self.model_name = model_name
        self.config = model_config
        self.logger = logger or ChengduAnalysisLogger(f"RegressionModel_{model_name}")
        
        # 模型相关属性
        self.dependent_var = model_config['dependent_variable']
        self.independent_vars = model_config['independent_variables']
        
        # 模型对象
        self.sklearn_model = None
        self.statsmodels_model = None
        self.fitted = False
        
        # 结果存储
        self.results = {}
        self.X_train = None
        self.y_train = None
        
        self.logger.info(f"初始化回归模型: {model_name}")
        self.logger.info(f"  因变量: {self.dependent_var}")
        self.logger.info(f"  自变量数量: {len(self.independent_vars)}")
    
    def prepare_data(self, df: pd.DataFrame) -> Tuple[bool, Optional[pd.DataFrame], Optional[pd.Series]]:
        """
        准备回归分析数据
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            Tuple[bool, Optional[pd.DataFrame], Optional[pd.Series]]: 
            (准备是否成功, 自变量DataFrame, 因变量Series)
        """
        try:
            # 检查必需列是否存在
            required_columns = [self.dependent_var] + self.independent_vars
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                self.logger.error(f"数据中缺少必需列: {missing_columns}")
                return False, None, None
            
            # 提取数据
            X = df[self.independent_vars].copy()
            y = df[self.dependent_var].copy()
            
            # 检查缺失值
            X_missing = X.isnull().sum().sum()
            y_missing = y.isnull().sum()
            
            if X_missing > 0 or y_missing > 0:
                self.logger.warning(f"发现缺失值 - 自变量: {X_missing}, 因变量: {y_missing}")
                
                # 删除包含缺失值的行
                initial_rows = len(X)
                mask = ~(X.isnull().any(axis=1) | y.isnull())
                X = X[mask]
                y = y[mask]
                final_rows = len(X)
                
                self.logger.info(f"删除缺失值后，数据从 {initial_rows} 行减少到 {final_rows} 行")
            
            # 检查数据量是否足够
            if len(X) < len(self.independent_vars) + 1:
                self.logger.error(f"数据量不足进行回归分析，需要至少 {len(self.independent_vars) + 1} 行数据，实际只有 {len(X)} 行")
                return False, None, None
            
            # 检查自变量是否存在多重共线性问题
            correlation_matrix = X.corr()
            high_corr_pairs = []
            
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_value = abs(correlation_matrix.iloc[i, j])
                    if corr_value > 0.9:  # 高度相关阈值
                        high_corr_pairs.append((
                            correlation_matrix.columns[i],
                            correlation_matrix.columns[j],
                            corr_value
                        ))
            
            if high_corr_pairs:
                self.logger.warning("发现高度相关的自变量对（可能存在多重共线性）:")
                for var1, var2, corr in high_corr_pairs:
                    self.logger.warning(f"  {var1} - {var2}: {corr:.4f}")
            
            self.X_train = X
            self.y_train = y
            
            self.logger.info(f"✓ 数据准备完成，样本数: {len(X)}, 自变量数: {len(X.columns)}")
            return True, X, y
            
        except Exception as e:
            self.logger.error(f"数据准备失败: {str(e)}")
            return False, None, None
    
    def fit_models(self, X: pd.DataFrame, y: pd.Series) -> bool:
        """
        拟合回归模型（同时使用sklearn和statsmodels）
        
        Args:
            X: 自变量DataFrame
            y: 因变量Series
            
        Returns:
            bool: 拟合是否成功
        """
        try:
            self.logger.info(f"开始拟合回归模型: {self.model_name}")
            
            # 1. 使用sklearn进行回归
            self.sklearn_model = LinearRegression()
            self.sklearn_model.fit(X, y)
            
            # 2. 使用statsmodels进行回归（包含统计检验）
            X_with_const = sm.add_constant(X)  # 添加常数项
            self.statsmodels_model = sm.OLS(y, X_with_const).fit()
            
            self.fitted = True
            self.logger.info("✓ 模型拟合完成")
            return True
            
        except Exception as e:
            self.logger.error(f"模型拟合失败: {str(e)}")
            return False
    
    def calculate_results(self) -> Dict[str, Any]:
        """
        计算回归分析结果
        
        Returns:
            Dict[str, Any]: 回归结果字典
        """
        if not self.fitted:
            self.logger.error("模型尚未拟合，无法计算结果")
            return {}
        
        try:
            self.logger.info("开始计算回归分析结果")
            
            # 预测值
            y_pred_sklearn = self.sklearn_model.predict(self.X_train)
            y_pred_statsmodels = self.statsmodels_model.predict()
            
            # 基本统计指标
            results = {
                'model_name': self.model_name,
                'dependent_variable': self.dependent_var,
                'independent_variables': self.independent_vars,
                'sample_size': len(self.y_train),
                'timestamp': get_current_timestamp(),
                
                # 模型系数（来自statsmodels，包含统计检验）
                'coefficients': {
                    'values': dict(zip(['const'] + self.independent_vars, self.statsmodels_model.params)),
                    'std_errors': dict(zip(['const'] + self.independent_vars, self.statsmodels_model.bse)),
                    't_values': dict(zip(['const'] + self.independent_vars, self.statsmodels_model.tvalues)),
                    'p_values': dict(zip(['const'] + self.independent_vars, self.statsmodels_model.pvalues)),
                    'confidence_intervals': dict(zip(['const'] + self.independent_vars, 
                                                   [tuple(ci) for ci in self.statsmodels_model.conf_int().values]))
                },
                
                # 模型拟合优度
                'model_fit': {
                    'r_squared': self.statsmodels_model.rsquared,
                    'adjusted_r_squared': self.statsmodels_model.rsquared_adj,
                    'f_statistic': self.statsmodels_model.fvalue,
                    'f_pvalue': self.statsmodels_model.f_pvalue,
                    'aic': self.statsmodels_model.aic,
                    'bic': self.statsmodels_model.bic,
                    'log_likelihood': self.statsmodels_model.llf
                },
                
                # 残差分析
                'residuals': {
                    'residuals': self.statsmodels_model.resid.tolist(),
                    'standardized_residuals': (self.statsmodels_model.resid / np.sqrt(self.statsmodels_model.mse_resid)).tolist(),
                    'mean_squared_error': mean_squared_error(self.y_train, y_pred_sklearn),
                    'root_mean_squared_error': np.sqrt(mean_squared_error(self.y_train, y_pred_sklearn))
                },
                
                # 预测值
                'predictions': {
                    'fitted_values': y_pred_statsmodels.tolist(),
                    'actual_values': self.y_train.tolist()
                }
            }
            
            # 格式化数值结果
            self.results = format_regression_results(results, OUTPUT_CONFIG['decimal_places'])
            
            self.logger.info("✓ 回归分析结果计算完成")
            self.logger.info(f"  R² = {self.results['model_fit']['r_squared']:.4f}")
            self.logger.info(f"  调整R² = {self.results['model_fit']['adjusted_r_squared']:.4f}")
            self.logger.info(f"  F统计量 = {self.results['model_fit']['f_statistic']:.4f} (p = {self.results['model_fit']['f_pvalue']:.4f})")
            
            return self.results
            
        except Exception as e:
            self.logger.error(f"计算回归结果失败: {str(e)}")
            return {}
    
    def get_significant_variables(self, alpha: float = 0.05) -> List[str]:
        """
        获取显著的自变量列表
        
        Args:
            alpha: 显著性水平
            
        Returns:
            List[str]: 显著变量名列表
        """
        if not self.results:
            return []
        
        significant_vars = []
        p_values = self.results.get('coefficients', {}).get('p_values', {})
        
        for var, p_value in p_values.items():
            if var != 'const' and p_value < alpha:
                significant_vars.append(var)
        
        return significant_vars


class RegressionAnalysisManager:
    """回归分析管理器，负责管理多个回归模型"""

    def __init__(self, logger: Optional[ChengduAnalysisLogger] = None):
        """
        初始化回归分析管理器

        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or ChengduAnalysisLogger("RegressionAnalysisManager")
        self.models = {}
        self.data = None

        # 初始化模型
        self._initialize_models()

    def _initialize_models(self):
        """初始化所有回归模型"""
        for model_name, model_config in MODEL_CONFIG.items():
            self.models[model_name] = RegressionModel(
                model_name=model_name,
                model_config=model_config,
                logger=self.logger
            )

        self.logger.info(f"初始化 {len(self.models)} 个回归模型")

    def run_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        运行完整的回归分析

        Args:
            df: 分析数据DataFrame

        Returns:
            Dict[str, Any]: 所有模型的分析结果
        """
        try:
            self.logger.info("开始运行回归分析")
            self.data = df.copy()

            all_results = {
                'analysis_timestamp': get_current_timestamp(),
                'data_summary': {
                    'total_observations': len(df),
                    'data_columns': list(df.columns),
                    'date_range': {
                        'start_date': str(df['date'].min()) if 'date' in df.columns else None,
                        'end_date': str(df['date'].max()) if 'date' in df.columns else None
                    }
                },
                'models': {}
            }

            # 对每个模型进行分析
            for model_name, model in self.models.items():
                self.logger.info(f"分析模型: {model_name}")

                # 准备数据
                success, X, y = model.prepare_data(df)
                if not success:
                    self.logger.error(f"模型 {model_name} 数据准备失败")
                    continue

                # 拟合模型
                if not model.fit_models(X, y):
                    self.logger.error(f"模型 {model_name} 拟合失败")
                    continue

                # 计算结果
                model_results = model.calculate_results()
                if model_results:
                    all_results['models'][model_name] = model_results

                    # 记录关键结果
                    r_squared = model_results.get('model_fit', {}).get('r_squared', 0)
                    significant_vars = model.get_significant_variables()

                    self.logger.info(f"✓ 模型 {model_name} 分析完成")
                    self.logger.info(f"  R² = {r_squared:.4f}")
                    self.logger.info(f"  显著变量数量: {len(significant_vars)}")
                else:
                    self.logger.error(f"模型 {model_name} 结果计算失败")

            # 生成分析摘要
            all_results['analysis_summary'] = self._generate_analysis_summary(all_results)

            self.logger.info(f"✓ 回归分析完成，成功分析 {len(all_results['models'])} 个模型")
            return all_results

        except Exception as e:
            self.logger.error(f"回归分析过程发生错误: {str(e)}")
            return {}

    def _generate_analysis_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成分析摘要

        Args:
            results: 完整分析结果

        Returns:
            Dict[str, Any]: 分析摘要
        """
        summary = {
            'total_models': len(results.get('models', {})),
            'successful_models': [],
            'model_comparison': {}
        }

        for model_name, model_results in results.get('models', {}).items():
            summary['successful_models'].append(model_name)

            # 提取关键指标用于比较
            model_fit = model_results.get('model_fit', {})
            summary['model_comparison'][model_name] = {
                'r_squared': model_fit.get('r_squared', 0),
                'adjusted_r_squared': model_fit.get('adjusted_r_squared', 0),
                'f_statistic': model_fit.get('f_statistic', 0),
                'f_pvalue': model_fit.get('f_pvalue', 1),
                'aic': model_fit.get('aic', float('inf')),
                'sample_size': model_results.get('sample_size', 0)
            }

        return summary

    def get_model_results(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取特定模型的结果

        Args:
            model_name: 模型名称

        Returns:
            Optional[Dict[str, Any]]: 模型结果
        """
        if model_name in self.models and self.models[model_name].results:
            return self.models[model_name].results
        return None

    def compare_models(self) -> Dict[str, Any]:
        """
        比较所有模型的性能

        Returns:
            Dict[str, Any]: 模型比较结果
        """
        comparison = {
            'comparison_timestamp': get_current_timestamp(),
            'models': {}
        }

        for model_name, model in self.models.items():
            if model.results:
                model_fit = model.results.get('model_fit', {})
                comparison['models'][model_name] = {
                    'r_squared': model_fit.get('r_squared', 0),
                    'adjusted_r_squared': model_fit.get('adjusted_r_squared', 0),
                    'aic': model_fit.get('aic', float('inf')),
                    'bic': model_fit.get('bic', float('inf')),
                    'f_pvalue': model_fit.get('f_pvalue', 1),
                    'significant_variables_count': len(model.get_significant_variables())
                }

        return comparison


if __name__ == "__main__":
    # 测试回归分析功能
    from data_processor import DataProcessor

    # 获取数据
    processor = DataProcessor()
    df = processor.get_processed_data()

    if df is not None:
        # 运行回归分析
        analyzer = RegressionAnalysisManager()
        results = analyzer.run_analysis(df)

        if results:
            print("✓ 回归分析模块测试完成")
            print(f"✓ 成功分析 {len(results.get('models', {}))} 个模型")

            # 显示模型比较
            comparison = analyzer.compare_models()
            print("✓ 模型比较结果:")
            for model_name, metrics in comparison.get('models', {}).items():
                print(f"  {model_name}: R² = {metrics['r_squared']:.4f}")
        else:
            print("✗ 回归分析测试失败")
    else:
        print("✗ 无法获取数据进行回归分析测试")
