"""
成都项目数据分析主程序
整合Excel数据读取、数据库存储、回归分析和结果输出的完整流程
"""

import sys
import traceback
from pathlib import Path
from typing import Optional

# 导入项目模块
from config import create_directories, DATA_CONFIG
from utils import ChengduAnalysisLogger
from data_processor import DataProcessor
from regression_analysis import RegressionAnalysisManager
from results_reporter import ResultsReporter


class ChengduAnalysisMain:
    """成都项目分析主控制器"""
    
    def __init__(self):
        """初始化主控制器"""
        # 创建必要的目录
        create_directories()
        
        # 初始化日志记录器
        self.logger = ChengduAnalysisLogger("ChengduAnalysisMain")
        
        # 初始化各个组件
        self.data_processor = DataProcessor(self.logger)
        self.regression_analyzer = RegressionAnalysisManager(self.logger)
        self.results_reporter = ResultsReporter(self.logger)
        
        self.logger.info("成都项目分析系统初始化完成")
    
    def run_complete_analysis(self, excel_file_path: Optional[Path] = None) -> bool:
        """
        运行完整的分析流程
        
        Args:
            excel_file_path: Excel文件路径，默认使用配置中的路径
            
        Returns:
            bool: 分析是否成功完成
        """
        try:
            self.logger.info("开始运行完整的成都项目数据分析流程")
            self.logger.info("=" * 80)
            
            # 第一步：数据处理（Excel读取 + 数据库存储）
            self.logger.info("第一步：数据处理和存储")
            self.logger.info("-" * 40)
            
            success = self.data_processor.process_excel_to_database(excel_file_path)
            if not success:
                self.logger.error("数据处理失败，终止分析流程")
                return False
            
            self.logger.info("✓ 数据处理完成")
            self.logger.info("")
            
            # 第二步：获取处理后的数据
            self.logger.info("第二步：获取分析数据")
            self.logger.info("-" * 40)
            
            df = self.data_processor.get_processed_data()
            if df is None:
                self.logger.error("无法获取处理后的数据，终止分析流程")
                return False
            
            self.logger.info(f"✓ 成功获取数据，数据形状: {df.shape}")
            self.logger.info(f"✓ 数据列: {list(df.columns)}")
            self.logger.info("")
            
            # 第三步：回归分析
            self.logger.info("第三步：回归分析")
            self.logger.info("-" * 40)
            
            analysis_results = self.regression_analyzer.run_analysis(df)
            if not analysis_results or not analysis_results.get('models'):
                self.logger.error("回归分析失败，终止分析流程")
                return False
            
            successful_models = len(analysis_results.get('models', {}))
            self.logger.info(f"✓ 回归分析完成，成功分析 {successful_models} 个模型")
            self.logger.info("")
            
            # 第四步：结果输出和报告生成
            self.logger.info("第四步：结果输出和报告生成")
            self.logger.info("-" * 40)
            
            report_success = self.results_reporter.generate_comprehensive_report(analysis_results)
            if not report_success:
                self.logger.warning("报告生成部分失败，但分析结果仍然有效")
            else:
                self.logger.info("✓ 综合报告生成完成")
            
            # 第五步：显示分析摘要
            self.logger.info("")
            self.logger.info("第五步：分析结果摘要")
            self.logger.info("-" * 40)
            
            summary = self.results_reporter.generate_summary_report(analysis_results)
            print("\n" + summary)
            
            # 显示输出文件位置
            self.logger.info("")
            self.logger.info("输出文件位置:")
            self.logger.info(f"  输出目录: {DATA_CONFIG['output_dir']}")
            self.logger.info(f"  数据库文件: {DATA_CONFIG['database_path']}")
            self.logger.info(f"  日志目录: {DATA_CONFIG['log_dir']}")
            
            self.logger.info("")
            self.logger.info("=" * 80)
            self.logger.info("✓ 成都项目数据分析流程全部完成！")
            
            return True
            
        except Exception as e:
            self.logger.error(f"分析流程发生严重错误: {str(e)}")
            self.logger.error("错误详情:")
            self.logger.error(traceback.format_exc())
            return False
    
    def run_analysis_only(self) -> bool:
        """
        仅运行回归分析（假设数据已经在数据库中）
        
        Returns:
            bool: 分析是否成功
        """
        try:
            self.logger.info("运行仅回归分析模式")
            
            # 获取数据
            df = self.data_processor.get_processed_data()
            if df is None:
                self.logger.error("无法从数据库获取数据，请先运行完整分析流程")
                return False
            
            # 运行回归分析
            analysis_results = self.regression_analyzer.run_analysis(df)
            if not analysis_results:
                self.logger.error("回归分析失败")
                return False
            
            # 生成报告
            report_success = self.results_reporter.generate_comprehensive_report(analysis_results)
            
            # 显示摘要
            summary = self.results_reporter.generate_summary_report(analysis_results)
            print("\n" + summary)
            
            return True
            
        except Exception as e:
            self.logger.error(f"仅分析模式发生错误: {str(e)}")
            return False
    
    def check_data_status(self) -> bool:
        """
        检查数据状态
        
        Returns:
            bool: 数据是否可用
        """
        try:
            self.logger.info("检查数据状态")
            
            # 检查Excel文件
            excel_file = DATA_CONFIG['excel_file_path']
            if excel_file.exists():
                self.logger.info(f"✓ Excel文件存在: {excel_file}")
            else:
                self.logger.error(f"✗ Excel文件不存在: {excel_file}")
                return False
            
            # 检查数据库
            db_file = DATA_CONFIG['database_path']
            if db_file.exists():
                self.logger.info(f"✓ 数据库文件存在: {db_file}")
                
                # 尝试获取数据
                df = self.data_processor.get_processed_data()
                if df is not None:
                    self.logger.info(f"✓ 数据库中有数据，数据形状: {df.shape}")
                    return True
                else:
                    self.logger.warning("数据库文件存在但无法获取数据")
            else:
                self.logger.info(f"数据库文件不存在: {db_file}")
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查数据状态时发生错误: {str(e)}")
            return False


def main():
    """主函数"""
    print("成都项目暑期数据回归分析系统")
    print("=" * 50)
    
    try:
        # 初始化分析系统
        analyzer = ChengduAnalysisMain()
        
        # 检查命令行参数
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()
            
            if command == "check":
                # 检查数据状态
                analyzer.check_data_status()
            elif command == "analysis":
                # 仅运行分析
                success = analyzer.run_analysis_only()
                if success:
                    print("\n✓ 分析完成")
                else:
                    print("\n✗ 分析失败")
            elif command == "help":
                # 显示帮助信息
                print("使用方法:")
                print("  python main.py          - 运行完整分析流程")
                print("  python main.py check    - 检查数据状态")
                print("  python main.py analysis - 仅运行回归分析")
                print("  python main.py help     - 显示此帮助信息")
            else:
                print(f"未知命令: {command}")
                print("使用 'python main.py help' 查看帮助信息")
        else:
            # 默认运行完整分析流程
            success = analyzer.run_complete_analysis()
            
            if success:
                print("\n🎉 分析成功完成！")
                print(f"📁 请查看输出目录: {DATA_CONFIG['output_dir']}")
            else:
                print("\n❌ 分析失败，请查看日志了解详情")
                sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n用户中断了程序执行")
    except Exception as e:
        print(f"\n程序执行发生严重错误: {str(e)}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
